import { logger } from '../utils/logger.js';
import { tradeService } from '../services/tradeService.js';
/**
 * Get initial trade data for a pool address (WebSocket first, then API fallback)
 * GET /api/trade/initial/:poolAddress
 */
export const getInitialTradeData = async (req, res) => {
    try {
        const { poolAddress } = req.params;
        const { limit, source } = req.query;
        if (!poolAddress) {
            res.status(400).json({
                success: false,
                error: 'Pool address is required'
            });
            return;
        }
        // Validate pool address format
        if (!tradeService.validatePoolAddress(poolAddress)) {
            res.status(400).json({
                success: false,
                error: 'Invalid pool address format'
            });
            return;
        }
        const limitNum = limit ? parseInt(limit, 10) : 20;
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            res.status(400).json({
                success: false,
                error: 'Limit must be a number between 1 and 100'
            });
            return;
        }
        logger.info(`Fetching initial trade data for pool: ${poolAddress}, limit: ${limitNum}, source preference: ${source || 'websocket-first'}`);
        const trades = await tradeService.fetchInitialTradeData(poolAddress, limitNum);
        const formattedTrades = tradeService.formatTradeData(trades);
        res.json({
            success: true,
            data: {
                poolAddress,
                trades: formattedTrades,
                count: formattedTrades.length,
                timestamp: Date.now(),
                source: trades.length > 0 ? 'websocket-or-api' : 'no-data'
            }
        });
        logger.info(`✅ Successfully returned ${formattedTrades.length} trades for pool: ${poolAddress}`);
    }
    catch (error) {
        logger.error('Error in getInitialTradeData controller:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
};
/**
 * Get paginated trade data for a pool address
 * GET /api/trade/paginated/:poolAddress
 */
export const getPaginatedTradeData = async (req, res) => {
    try {
        const { poolAddress } = req.params;
        const { limit, page, sortOrder, fromTimestamp, toTimestamp } = req.query;
        if (!poolAddress) {
            res.status(400).json({
                success: false,
                error: 'Pool address is required'
            });
            return;
        }
        // Validate pool address format
        if (!tradeService.validatePoolAddress(poolAddress)) {
            res.status(400).json({
                success: false,
                error: 'Invalid pool address format'
            });
            return;
        }
        // Parse and validate query parameters
        const limitNum = limit ? parseInt(limit, 10) : 20;
        const pageNum = page ? parseInt(page, 10) : 1;
        const sortOrderStr = sortOrder || 'desc';
        const fromTimestampNum = fromTimestamp ? parseInt(fromTimestamp, 10) : undefined;
        const toTimestampNum = toTimestamp ? parseInt(toTimestamp, 10) : undefined;
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            res.status(400).json({
                success: false,
                error: 'Limit must be a number between 1 and 100'
            });
            return;
        }
        if (isNaN(pageNum) || pageNum < 1) {
            res.status(400).json({
                success: false,
                error: 'Page must be a positive number'
            });
            return;
        }
        if (sortOrderStr !== 'asc' && sortOrderStr !== 'desc') {
            res.status(400).json({
                success: false,
                error: 'Sort order must be either "asc" or "desc"'
            });
            return;
        }
        logger.info(`Fetching paginated trade data for pool: ${poolAddress}, page: ${pageNum}, limit: ${limitNum}`);
        const result = await tradeService.fetchTradeDataWithPagination(poolAddress, {
            limit: limitNum,
            page: pageNum,
            sortOrder: sortOrderStr,
            fromTimestamp: fromTimestampNum,
            toTimestamp: toTimestampNum
        });
        const formattedTrades = tradeService.formatTradeData(result.trades);
        res.json({
            success: true,
            data: {
                poolAddress,
                trades: formattedTrades,
                pagination: result.pagination,
                timestamp: Date.now()
            }
        });
        logger.info(`✅ Successfully returned ${formattedTrades.length} trades for pool: ${poolAddress} (page ${pageNum})`);
    }
    catch (error) {
        logger.error('Error in getPaginatedTradeData controller:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
};
/**
 * Validate pool address format
 * GET /api/trade/validate/:poolAddress
 */
export const validatePoolAddress = async (req, res) => {
    try {
        const { poolAddress } = req.params;
        if (!poolAddress) {
            res.status(400).json({
                success: false,
                error: 'Pool address is required'
            });
            return;
        }
        const isValid = tradeService.validatePoolAddress(poolAddress);
        res.json({
            success: true,
            data: {
                poolAddress,
                isValid,
                timestamp: Date.now()
            }
        });
    }
    catch (error) {
        logger.error('Error in validatePoolAddress controller:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
};
/**
 * Get trade service health status
 * GET /api/trade/health
 */
export const getTradeServiceHealth = async (req, res) => {
    try {
        const healthStatus = await tradeService.getHealthStatus();
        const statusCode = healthStatus.status === 'healthy' ? 200 :
            healthStatus.status === 'degraded' ? 206 : 503;
        res.status(statusCode).json({
            success: healthStatus.status === 'healthy',
            ...healthStatus
        });
    }
    catch (error) {
        logger.error('Error in getTradeServiceHealth controller:', error);
        res.status(500).json({
            success: false,
            status: 'unhealthy',
            message: 'Failed to check service health',
            timestamp: Date.now()
        });
    }
};
/**
 * Get trade WebSocket service status
 * GET /api/trade/websocket/status
 */
export const getTradeWebSocketStatus = async (req, res) => {
    try {
        // Lazy import to avoid circular dependency
        const { mobulaTradeWebSocketService } = await import('../services/mobulaTradeWebSocketService.js');
        const status = mobulaTradeWebSocketService.getStatus();
        res.json({
            success: true,
            data: {
                ...status,
                timestamp: Date.now()
            }
        });
    }
    catch (error) {
        logger.error('Error in getTradeWebSocketStatus controller:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get WebSocket status',
            message: error.message,
            timestamp: Date.now()
        });
    }
};
/**
 * Get trade data from WebSocket cache only (no API fallback)
 * GET /api/trade/websocket/:poolAddress
 */
export const getWebSocketTradeData = async (req, res) => {
    try {
        const { poolAddress } = req.params;
        const { limit } = req.query;
        if (!poolAddress) {
            res.status(400).json({
                success: false,
                error: 'Pool address is required'
            });
            return;
        }
        // Validate pool address format
        if (!tradeService.validatePoolAddress(poolAddress)) {
            res.status(400).json({
                success: false,
                error: 'Invalid pool address format'
            });
            return;
        }
        const limitNum = limit ? parseInt(limit, 10) : 20;
        if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
            res.status(400).json({
                success: false,
                error: 'Limit must be a number between 1 and 100'
            });
            return;
        }
        logger.info(`Fetching WebSocket trade data for pool: ${poolAddress}, limit: ${limitNum}`);
        try {
            // Lazy import to avoid circular dependency
            const { mobulaTradeWebSocketService } = await import('../services/mobulaTradeWebSocketService.js');
            // Get cached trade history from WebSocket service
            const wsTradeHistory = mobulaTradeWebSocketService.getTradeHistory(poolAddress);
            // Convert WebSocket format to API format
            const convertedTrades = wsTradeHistory.map((wsTrade, index) => ({
                id: `ws-${wsTrade.timestamp}-${index}`,
                timestamp: wsTrade.timestamp,
                type: wsTrade.type,
                amount: wsTrade.token_amount_usd || 0,
                price: wsTrade.pairData?.price || 0,
                value_usd: wsTrade.token_amount_usd || 0,
                token_amount: wsTrade.pairData?.token_amount || 0,
                token_amount_usd: wsTrade.token_amount_usd || 0,
                pair: wsTrade.pair,
                blockchain: 'solana',
                dex: wsTrade.pairData?.dex || 'unknown',
                wallet: wsTrade.pairData?.wallet || 'unknown',
                tx_hash: wsTrade.pairData?.tx_hash || 'unknown'
            }));
            const limitedTrades = convertedTrades.slice(0, limitNum);
            const formattedTrades = tradeService.formatTradeData(limitedTrades);
            res.json({
                success: true,
                data: {
                    poolAddress,
                    trades: formattedTrades,
                    count: formattedTrades.length,
                    timestamp: Date.now(),
                    source: 'websocket-cache',
                    cacheSize: wsTradeHistory.length
                }
            });
            logger.info(`✅ Successfully returned ${formattedTrades.length} trades from WebSocket cache for pool: ${poolAddress}`);
        }
        catch (wsError) {
            logger.error('Error accessing WebSocket trade data:', wsError);
            res.json({
                success: true,
                data: {
                    poolAddress,
                    trades: [],
                    count: 0,
                    timestamp: Date.now(),
                    source: 'websocket-cache',
                    error: 'WebSocket service unavailable'
                }
            });
        }
    }
    catch (error) {
        logger.error('Error in getWebSocketTradeData controller:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error',
            message: error.message
        });
    }
};
/**
 * Get frontend WebSocket service trade statistics
 * GET /api/trade/frontend/stats
 */
export const getFrontendTradeStats = async (req, res) => {
    try {
        // Lazy import to avoid circular dependency
        const { frontendWebSocketService } = await import('../services/frontendWebSocketService.js');
        const stats = frontendWebSocketService.getStats();
        res.json({
            success: true,
            data: {
                tradeRoomClients: stats.tradeRoomClients,
                activeTradeSubscriptions: stats.activeTradeSubscriptions,
                tradeSubscriptionsByPool: stats.tradeSubscriptionsByPool,
                timestamp: Date.now()
            }
        });
    }
    catch (error) {
        logger.error('Error in getFrontendTradeStats controller:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get frontend trade stats',
            message: error.message,
            timestamp: Date.now()
        });
    }
};
//# sourceMappingURL=tradeController.js.map