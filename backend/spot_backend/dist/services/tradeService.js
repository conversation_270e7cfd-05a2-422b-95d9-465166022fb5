import axios from 'axios';
import { logger } from '../utils/logger.js';
import { getApiKey } from './coinService.js';
/**
 * Service for fetching trade data from Mobula API
 */
class TradeService {
    baseUrl = 'https://api.mobula.io/api/1';
    defaultLimit = 20;
    timeout = 25000;
    /**
     * Fetch initial trade data for a pool address
     * First tries WebSocket cache, then falls back to API
     */
    async fetchInitialTradeData(poolAddress, limit = this.defaultLimit) {
        try {
            logger.info(`Fetching initial trade data for pool: ${poolAddress}`);
            // Always subscribe to WebSocket for this pool to start receiving data
            await this.subscribeToWebSocketUpdates(poolAddress);
            // 1. First try to get data from WebSocket service cache
            const wsData = await this.getWebSocketTradeData(poolAddress, limit);
            if (wsData.length > 0) {
                logger.info(`✅ Retrieved ${wsData.length} trades from WebSocket cache for pool: ${poolAddress}`);
                return wsData;
            }
            // 2. Fall back to API if WebSocket has no data
            logger.info(`📡 No WebSocket data available, falling back to API for pool: ${poolAddress}`);
            const apiKey = await getApiKey();
            if (!apiKey) {
                logger.error('❌ No API key available for Mobula trade data request');
                return [];
            }
            const url = `${this.baseUrl}/market/trades/pair`;
            const params = {
                address: poolAddress,
                blockchain: 'solana',
                limit,
                sortOrder: 'desc'
            };
            logger.info(`Making Mobula API request: ${url}`, { params });
            const response = await axios.get(url, {
                params,
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    accept: 'application/json'
                },
                timeout: this.timeout
            });
            logger.info(`Mobula API response status: ${response.status}`, {
                dataKeys: Object.keys(response.data || {}),
                tradesCount: response.data?.data?.trades?.length || 0
            });
            const trades = response.data.data?.trades || [];
            if (trades.length > 0) {
                logger.info(`✅ Successfully fetched ${trades.length} initial trades from API for pool: ${poolAddress}`);
                return trades;
            }
            else {
                logger.info(`📊 No trades found for pool: ${poolAddress}`);
                return [];
            }
        }
        catch (error) {
            logger.error(`❌ Error fetching initial trade data for pool ${poolAddress}:`, {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data,
                url: error.config?.url,
                params: error.config?.params
            });
            logger.info(`📊 No trade data available for pool: ${poolAddress}`);
            return [];
        }
    }
    /**
     * Fetch trade data with pagination support
     * First tries WebSocket cache, then falls back to API
     */
    async fetchTradeDataWithPagination(poolAddress, options = {}) {
        try {
            const { limit = this.defaultLimit, page = 1, sortOrder = 'desc', fromTimestamp, toTimestamp } = options;
            logger.info(`Fetching paginated trade data for pool: ${poolAddress}, page: ${page}, limit: ${limit}`);
            // 1. First try WebSocket cache for first page without timestamp filters
            if (page === 1 && !fromTimestamp && !toTimestamp) {
                const wsData = await this.getWebSocketTradeData(poolAddress, limit);
                if (wsData.length > 0) {
                    // Apply sort order to WebSocket data
                    const sortedData = sortOrder === 'asc' ? wsData.reverse() : wsData;
                    logger.info(`✅ Retrieved ${sortedData.length} trades from WebSocket cache for pool: ${poolAddress}`);
                    return {
                        trades: sortedData,
                        pagination: {
                            total: sortedData.length,
                            page: 1,
                            limit,
                            hasMore: false // WebSocket cache is limited, so no more pages
                        }
                    };
                }
            }
            // 2. Fall back to API for pagination or when WebSocket has no data
            logger.info(`📡 Using API for paginated data for pool: ${poolAddress}`);
            const apiKey = await getApiKey();
            if (!apiKey) {
                logger.error('❌ No API key available for Mobula trade data request');
                return { trades: [] };
            }
            const params = {
                address: poolAddress,
                blockchain: 'solana',
                limit,
                sortOrder
            };
            // Add optional timestamp filters
            if (fromTimestamp) {
                params.from = fromTimestamp;
            }
            if (toTimestamp) {
                params.to = toTimestamp;
            }
            const response = await axios.get(`${this.baseUrl}/market/trades/pair`, {
                params,
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    accept: 'application/json'
                },
                timeout: this.timeout
            });
            const trades = response.data.data?.trades || [];
            const pagination = response.data.data?.pagination;
            // Subscribe to WebSocket for future updates if we got data
            if (trades.length > 0 && page === 1) {
                await this.subscribeToWebSocketUpdates(poolAddress);
            }
            logger.info(`✅ Successfully fetched ${trades.length} trades from API for pool: ${poolAddress}`);
            return {
                trades,
                pagination: pagination ? {
                    ...pagination,
                    hasMore: pagination.page * pagination.limit < pagination.total
                } : undefined
            };
        }
        catch (error) {
            logger.error(`❌ Error fetching paginated trade data for pool ${poolAddress}:`, error.message || error);
            return { trades: [] };
        }
    }
    /**
     * Validate pool address format
     */
    validatePoolAddress(poolAddress) {
        if (!poolAddress || typeof poolAddress !== 'string') {
            return false;
        }
        // Basic Solana address validation (base58, 32-44 characters)
        const solanaAddressRegex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
        return solanaAddressRegex.test(poolAddress);
    }
    /**
     * Format trade data for frontend consumption
     */
    formatTradeData(trades) {
        return trades.map(trade => ({
            id: trade.id,
            timestamp: trade.timestamp,
            type: trade.type,
            amount: trade.amount,
            price: trade.price,
            valueUsd: trade.value_usd,
            tokenAmount: trade.token_amount,
            tokenAmountUsd: trade.token_amount_usd,
            pair: trade.pair,
            blockchain: trade.blockchain,
            dex: trade.dex,
            wallet: trade.wallet,
            txHash: trade.tx_hash,
            // Mobula-specific fields
            solAmount: trade.sol_amount || 0,
            tokenSymbol: trade.token_symbol || 'TOKEN',
            tokenName: trade.token_name || 'Unknown Token',
            exchange: trade.exchange || trade.dex || 'Unknown DEX',
            marketCap: trade.market_cap || 0,
            liquidity: trade.liquidity || 0,
            volume24h: trade.volume_24h || 0,
            priceChange24h: trade.price_change_24h || 0,
            // Add formatted display values
            displayAmount: this.formatNumber(trade.token_amount),
            displayPrice: this.formatPrice(trade.price),
            displayValueUsd: this.formatCurrency(trade.value_usd),
            displaySolAmount: this.formatNumber(trade.sol_amount || 0),
            displayMarketCap: this.formatCurrency(trade.market_cap || 0),
            displayVolume24h: this.formatCurrency(trade.volume_24h || 0),
            displayPriceChange24h: this.formatPercentage(trade.price_change_24h || 0),
            timeAgo: this.getTimeAgo(trade.timestamp),
            // Calculate additional useful values
            totalSOL: trade.sol_amount || (trade.value_usd / 146.32), // Fallback SOL calculation
            totalUSD: trade.value_usd,
            tipAndPrio: {
                tip: 0, // This would need to be calculated from actual tip data
                priority: this.getPriorityLevel(trade.value_usd)
            }
        }));
    }
    /**
     * Format number for display
     */
    formatNumber(num) {
        if (num >= 1e9) {
            return (num / 1e9).toFixed(2) + 'B';
        }
        else if (num >= 1e6) {
            return (num / 1e6).toFixed(2) + 'M';
        }
        else if (num >= 1e3) {
            return (num / 1e3).toFixed(2) + 'K';
        }
        else {
            return num.toFixed(2);
        }
    }
    /**
     * Format price for display
     */
    formatPrice(price) {
        if (price < 0.001) {
            return price.toExponential(3);
        }
        else if (price < 1) {
            return price.toFixed(6);
        }
        else {
            return price.toFixed(4);
        }
    }
    /**
     * Format currency for display
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }
    /**
     * Format percentage for display
     */
    formatPercentage(percentage) {
        const sign = percentage >= 0 ? '+' : '';
        return `${sign}${percentage.toFixed(2)}%`;
    }
    /**
     * Get priority level based on trade value
     */
    getPriorityLevel(valueUsd) {
        if (valueUsd >= 10000)
            return 'High';
        if (valueUsd >= 1000)
            return 'Medium';
        return 'Low';
    }
    /**
     * Get time ago string
     */
    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - (timestamp * 1000); // Convert to milliseconds
        const seconds = Math.floor(diff / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0) {
            return `${days}d ago`;
        }
        else if (hours > 0) {
            return `${hours}h ago`;
        }
        else if (minutes > 0) {
            return `${minutes}m ago`;
        }
        else {
            return `${seconds}s ago`;
        }
    }
    /**
     * Get trade data from WebSocket service cache
     */
    async getWebSocketTradeData(poolAddress, limit) {
        try {
            // Lazy import to avoid circular dependency
            const { mobulaTradeWebSocketService } = await import('./mobulaTradeWebSocketService.js');
            // Get cached trade history from WebSocket service
            const wsTradeHistory = mobulaTradeWebSocketService.getTradeHistory(poolAddress);
            if (wsTradeHistory.length > 0) {
                // Convert WebSocket format to API format
                const convertedTrades = this.convertWebSocketToApiFormat(wsTradeHistory);
                // Return the requested number of trades
                const limitedTrades = convertedTrades.slice(0, limit);
                logger.info(`📊 Found ${limitedTrades.length} trades in WebSocket cache for pool: ${poolAddress}`);
                return limitedTrades;
            }
            return [];
        }
        catch (error) {
            logger.error(`❌ Error accessing WebSocket trade data for pool ${poolAddress}:`, error);
            return [];
        }
    }
    /**
     * Convert WebSocket trade data format to API format
     */
    convertWebSocketToApiFormat(wsTradeData) {
        return wsTradeData.map((wsTrade, index) => {
            // Handle both old and new Mobula data formats
            const timestamp = wsTrade.timestamp || Math.floor((wsTrade.date || Date.now()) / 1000);
            const tokenPrice = wsTrade.token_price || wsTrade.pairData?.price || 0;
            const tokenAmount = wsTrade.token_amount || 0;
            const tokenAmountVs = wsTrade.token_amount_vs || 0; // SOL amount
            const tokenAmountUsd = wsTrade.token_amount_usd || 0;
            return {
                id: `ws-${timestamp}-${index}`,
                timestamp: timestamp,
                type: wsTrade.type,
                amount: tokenAmount, // Token amount
                price: tokenPrice, // Token price in USD
                value_usd: tokenAmountUsd, // Total USD value
                token_amount: tokenAmount,
                token_amount_usd: tokenAmountUsd,
                pair: wsTrade.pair,
                blockchain: wsTrade.blockchain || 'solana',
                dex: wsTrade.pairData?.exchange?.name || 'unknown',
                wallet: wsTrade.sender || 'unknown',
                tx_hash: wsTrade.hash || 'unknown',
                // Additional Mobula-specific data
                sol_amount: tokenAmountVs, // SOL amount from token_amount_vs
                token_symbol: wsTrade.pairData?.token0?.symbol || 'TOKEN',
                token_name: wsTrade.pairData?.token0?.name || 'Unknown Token',
                exchange: wsTrade.pairData?.exchange?.name || 'Unknown DEX',
                market_cap: wsTrade.pairData?.token0?.marketCap || 0,
                liquidity: wsTrade.pairData?.liquidity || 0,
                volume_24h: wsTrade.pairData?.volume_24h || 0,
                price_change_24h: wsTrade.pairData?.price_change_24h || 0
            };
        });
    }
    /**
     * Subscribe to WebSocket updates for a pool
     */
    async subscribeToWebSocketUpdates(poolAddress) {
        try {
            // Lazy import to avoid circular dependency
            const { mobulaTradeWebSocketService } = await import('./mobulaTradeWebSocketService.js');
            // Subscribe to the pool for real-time updates
            mobulaTradeWebSocketService.subscribeToPool(poolAddress, 'trade-service');
            logger.info(`📡 Subscribed to WebSocket updates for pool: ${poolAddress}`);
        }
        catch (error) {
            logger.error(`❌ Error subscribing to WebSocket updates for pool ${poolAddress}:`, error);
        }
    }
    /**
     * Get service health status
     */
    async getHealthStatus() {
        try {
            const apiKey = await getApiKey();
            if (!apiKey) {
                return {
                    status: 'unhealthy',
                    message: 'No API key available',
                    timestamp: Date.now()
                };
            }
            // Test with a simple request
            const testResponse = await axios.get(`${this.baseUrl}/market/trades/pair`, {
                params: {
                    address: '********************************************', // Test pool address
                    blockchain: 'solana',
                    limit: 1
                },
                headers: {
                    Authorization: `Bearer ${apiKey}`,
                    accept: 'application/json'
                },
                timeout: 5000
            });
            return {
                status: 'healthy',
                message: 'Trade service is operational',
                timestamp: Date.now()
            };
        }
        catch (error) {
            return {
                status: 'degraded',
                message: `Trade service error: ${error.message}`,
                timestamp: Date.now()
            };
        }
    }
}
// Export singleton instance
export const tradeService = new TradeService();
//# sourceMappingURL=tradeService.js.map