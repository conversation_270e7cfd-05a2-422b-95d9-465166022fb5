import React, { useState } from 'react';

interface TradeDataTestProps {
  poolAddress?: string;
}

const TradeDataTest: React.FC<TradeDataTestProps> = ({ 
  poolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe' 
}) => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testWebSocketFirst = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      console.log('🧪 Testing WebSocket-first trade data loading...');
      
      // Test 1: WebSocket-only endpoint
      const wsResponse = await fetch(`/api/trade/websocket/${poolAddress}?limit=5`);
      const wsData = await wsResponse.json();
      
      // Test 2: WebSocket-first endpoint (falls back to API)
      const hybridResponse = await fetch(`/api/trade/initial/${poolAddress}?limit=5`);
      const hybridData = await hybridResponse.json();
      
      setResults({
        websocketOnly: {
          success: wsData.success,
          count: wsData.data?.count || 0,
          source: wsData.data?.source,
          cacheSize: wsData.data?.cacheSize,
          trades: wsData.data?.trades?.slice(0, 2) || [] // Show first 2 trades
        },
        websocketFirst: {
          success: hybridData.success,
          count: hybridData.data?.count || 0,
          source: hybridData.data?.source,
          trades: hybridData.data?.trades?.slice(0, 2) || [] // Show first 2 trades
        }
      });
      
      console.log('✅ Test completed successfully');
    } catch (err: any) {
      console.error('❌ Test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-gray-900 rounded-lg border border-gray-700">
      <h3 className="text-xl font-bold text-white mb-4">
        🧪 Trade Data WebSocket Test
      </h3>
      
      <div className="mb-4">
        <p className="text-gray-300 text-sm mb-2">
          Pool Address: <code className="bg-gray-800 px-2 py-1 rounded">{poolAddress}</code>
        </p>
        
        <button
          onClick={testWebSocketFirst}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-4 py-2 rounded transition-colors"
        >
          {loading ? '🔄 Testing...' : '🚀 Test WebSocket-First Loading'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900 border border-red-700 rounded">
          <p className="text-red-300">❌ Error: {error}</p>
        </div>
      )}

      {results && (
        <div className="space-y-4">
          {/* WebSocket Only Results */}
          <div className="p-4 bg-gray-800 rounded border border-gray-600">
            <h4 className="text-lg font-semibold text-white mb-2">
              📡 WebSocket Only (/api/trade/websocket)
            </h4>
            <div className="text-sm text-gray-300 space-y-1">
              <p>✅ Success: {results.websocketOnly.success ? 'Yes' : 'No'}</p>
              <p>📊 Count: {results.websocketOnly.count}</p>
              <p>🔗 Source: {results.websocketOnly.source}</p>
              <p>💾 Cache Size: {results.websocketOnly.cacheSize}</p>
              
              {results.websocketOnly.trades.length > 0 && (
                <div className="mt-2">
                  <p className="font-medium">Sample Trades:</p>
                  <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-x-auto">
                    {JSON.stringify(results.websocketOnly.trades, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* WebSocket First Results */}
          <div className="p-4 bg-gray-800 rounded border border-gray-600">
            <h4 className="text-lg font-semibold text-white mb-2">
              🔄 WebSocket First (/api/trade/initial)
            </h4>
            <div className="text-sm text-gray-300 space-y-1">
              <p>✅ Success: {results.websocketFirst.success ? 'Yes' : 'No'}</p>
              <p>📊 Count: {results.websocketFirst.count}</p>
              <p>🔗 Source: {results.websocketFirst.source}</p>
              
              {results.websocketFirst.trades.length > 0 && (
                <div className="mt-2">
                  <p className="font-medium">Sample Trades:</p>
                  <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-x-auto">
                    {JSON.stringify(results.websocketFirst.trades, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Comparison */}
          <div className="p-4 bg-blue-900 rounded border border-blue-700">
            <h4 className="text-lg font-semibold text-white mb-2">
              📈 Comparison
            </h4>
            <div className="text-sm text-blue-200 space-y-1">
              <p>
                🏆 WebSocket Cache: {results.websocketOnly.count} trades available
              </p>
              <p>
                🔄 Hybrid Approach: {results.websocketFirst.count} trades loaded 
                (source: {results.websocketFirst.source})
              </p>
              <p className="mt-2 text-blue-100">
                💡 The hybrid approach tries WebSocket first, then falls back to API if needed.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeDataTest;
