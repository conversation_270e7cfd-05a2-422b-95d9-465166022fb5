import React, { useState, useEffect } from 'react';
import { useTradeData } from '../../hooks/useTradeData';

interface Trade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  price: number;
  amount: number;
  totalSOL: number;
  totalUSD: number;
  wallet: string;
  txHash: string;
  timeAgo: string;
}

interface CompactTradesTableProps {
  poolAddress: string;
  className?: string;
  maxTrades?: number;
}

const CompactTradesTable: React.FC<CompactTradesTableProps> = ({ 
  poolAddress, 
  className = '',
  maxTrades = 100
}) => {
  const { trades, isLoading, error, isConnected } = useTradeData(poolAddress);
  const [displayTrades, setDisplayTrades] = useState<Trade[]>([]);

  useEffect(() => {
    if (trades && trades.length > 0) {
      setDisplayTrades(trades.slice(0, maxTrades));
    }
  }, [trades, maxTrades]);

  const formatTime = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - (timestamp * 1000);
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return `${seconds}s`;
  };

  const formatPrice = (price: number): string => {
    if (price < 0.000001) {
      return `$${price.toExponential(1)}`;
    } else if (price < 0.001) {
      return `$${(price * 1000000).toFixed(0)}μ`;
    } else if (price < 1) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };

  const formatAmount = (amount: number): string => {
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(1)}B`;
    } else if (amount >= 1e6) {
      return `${(amount / 1e6).toFixed(1)}M`;
    } else if (amount >= 1e3) {
      return `${(amount / 1e3).toFixed(1)}K`;
    } else if (amount < 1) {
      return amount.toFixed(3);
    } else {
      return amount.toFixed(1);
    }
  };

  const formatSOL = (sol: number): string => {
    if (sol < 0.001) {
      return `◎ ${(sol * 1000).toFixed(1)}m`;
    } else if (sol < 1) {
      return `◎ ${sol.toFixed(3)}`;
    } else {
      return `◎ ${sol.toFixed(2)}`;
    }
  };

  const truncateWallet = (wallet: string): string => {
    if (!wallet || wallet === 'unknown') return '---';
    return `${wallet.slice(0, 3)}...${wallet.slice(-3)}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (isLoading) {
    return (
      <div className={`bg-black border border-gray-800 ${className}`}>
        <div className="p-4 text-center text-gray-500 text-sm">
          Loading trades...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-black border border-gray-800 ${className}`}>
        <div className="p-4 text-center text-red-400 text-sm">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-black border border-gray-800 ${className}`}>
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-800 bg-gray-900">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-white text-sm font-medium">Trades</span>
            <div className="flex items-center space-x-1">
              <div className={`w-1.5 h-1.5 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-gray-400 text-xs">
                {displayTrades.length}
              </span>
            </div>
          </div>
          <div className="text-xs text-gray-500">
            {isConnected ? 'Live' : 'Offline'}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden">
        <table className="w-full text-xs">
          {/* Header Row */}
          <thead>
            <tr className="border-b border-gray-800 bg-gray-900">
              <th className="text-left px-2 py-1.5 text-gray-400 font-normal">Age</th>
              <th className="text-left px-2 py-1.5 text-gray-400 font-normal">Type</th>
              <th className="text-right px-2 py-1.5 text-gray-400 font-normal">Price</th>
              <th className="text-right px-2 py-1.5 text-gray-400 font-normal">Amount</th>
              <th className="text-right px-2 py-1.5 text-gray-400 font-normal">Total SOL</th>
              <th className="text-center px-2 py-1.5 text-gray-400 font-normal">Trader</th>
            </tr>
          </thead>

          {/* Body */}
          <tbody>
            {displayTrades.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-2 py-6 text-center text-gray-500 text-sm">
                  No trades available
                </td>
              </tr>
            ) : (
              displayTrades.map((trade, index) => (
                <tr 
                  key={trade.id || index} 
                  className="border-b border-gray-900 hover:bg-gray-900 transition-colors"
                >
                  {/* Age */}
                  <td className="px-2 py-1.5 text-gray-400 font-mono">
                    {formatTime(trade.timestamp)}
                  </td>

                  {/* Type */}
                  <td className="px-2 py-1.5">
                    <span className={`text-xs font-medium ${
                      trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {trade.type === 'buy' ? 'Buy' : 'Sell'}
                    </span>
                  </td>

                  {/* Price */}
                  <td className="px-2 py-1.5 text-white font-mono text-right">
                    {formatPrice(trade.price)}
                  </td>

                  {/* Amount */}
                  <td className="px-2 py-1.5 text-gray-300 font-mono text-right">
                    {formatAmount(trade.amount)}
                  </td>

                  {/* Total SOL */}
                  <td className="px-2 py-1.5 text-white font-mono text-right">
                    {formatSOL(trade.totalSOL)}
                  </td>

                  {/* Trader */}
                  <td className="px-2 py-1.5 text-center">
                    <div className="flex items-center justify-center space-x-1">
                      <button
                        onClick={() => copyToClipboard(trade.wallet)}
                        className="text-blue-400 hover:text-blue-300 font-mono transition-colors"
                        title={`Copy: ${trade.wallet}`}
                      >
                        {truncateWallet(trade.wallet)}
                      </button>
                      {trade.txHash && trade.txHash !== 'unknown' && (
                        <a
                          href={`https://solscan.io/tx/${trade.txHash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-500 hover:text-gray-300 transition-colors"
                          title="View transaction"
                        >
                          <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.25 5.5a.75.75 0 00-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 00.75-.75v-4a.75.75 0 011.5 0v4A2.25 2.25 0 0112.75 17h-8.5A2.25 2.25 0 012 14.75v-8.5A2.25 2.25 0 014.25 4h5a.75.75 0 010 1.5h-5z" clipRule="evenodd" />
                            <path fillRule="evenodd" d="M6.194 12.753a.75.75 0 001.06.053L16.5 4.44v2.81a.75.75 0 001.5 0v-4.5a.75.75 0 00-.75-.75h-4.5a.75.75 0 000 1.5h2.553l-9.056 8.194a.75.75 0 00-.053 1.06z" clipRule="evenodd" />
                          </svg>
                        </a>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CompactTradesTable;
