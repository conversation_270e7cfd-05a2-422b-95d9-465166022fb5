import React, { useState, useEffect } from 'react';
import { useTradeData } from '../../hooks/useTradeData';

interface Trade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  price: number;
  amount: number;
  totalSOL: number;
  totalUSD: number;
  wallet: string;
  txHash: string;
  timeAgo: string;
}

interface TradesTableProps {
  poolAddress: string;
  className?: string;
}

const TradesTable: React.FC<TradesTableProps> = ({ poolAddress, className = '' }) => {
  const { trades, isLoading, error, isConnected } = useTradeData(poolAddress);
  const [displayTrades, setDisplayTrades] = useState<Trade[]>([]);

  useEffect(() => {
    if (trades && trades.length > 0) {
      setDisplayTrades(trades.slice(0, 50)); // Show last 50 trades
    }
  }, [trades]);

  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  const formatPrice = (price: number): string => {
    if (price < 0.000001) {
      return price.toExponential(2);
    } else if (price < 0.01) {
      return price.toFixed(6);
    } else {
      return price.toFixed(4);
    }
  };

  const formatAmount = (amount: number): string => {
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(2)}B`;
    } else if (amount >= 1e6) {
      return `${(amount / 1e6).toFixed(2)}M`;
    } else if (amount >= 1e3) {
      return `${(amount / 1e3).toFixed(2)}K`;
    } else {
      return amount.toFixed(2);
    }
  };

  const formatSOL = (sol: number): string => {
    if (sol < 0.001) {
      return sol.toFixed(6);
    } else if (sol < 1) {
      return sol.toFixed(4);
    } else {
      return sol.toFixed(2);
    }
  };

  const truncateWallet = (wallet: string): string => {
    if (!wallet || wallet === 'unknown') return '---';
    return `${wallet.slice(0, 3)}...${wallet.slice(-3)}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  if (isLoading) {
    return (
      <div className={`bg-gray-900 rounded-lg border border-gray-700 ${className}`}>
        <div className="p-6 text-center text-gray-400">
          <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          Loading trades...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-gray-900 rounded-lg border border-gray-700 ${className}`}>
        <div className="p-6 text-center text-red-400">
          ❌ Error loading trades: {error}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-900 rounded-lg border border-gray-700 ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-700 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h3 className="text-white font-semibold">Trades</h3>
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-gray-400">
              {isConnected ? 'Live' : 'Disconnected'}
            </span>
          </div>
        </div>
        <div className="text-sm text-gray-400">
          {displayTrades.length} trades
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            {/* Table Header */}
            <thead className="bg-gray-800 border-b border-gray-700">
              <tr>
                <th className="text-left px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Time
                </th>
                <th className="text-left px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="text-right px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Price
                </th>
                <th className="text-right px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
                <th className="text-right px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Total SOL
                </th>
                <th className="text-center px-3 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Trader
                </th>
              </tr>
            </thead>

            {/* Table Body */}
            <tbody className="divide-y divide-gray-800">
              {displayTrades.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-3 py-8 text-center text-gray-500">
                    No trades available
                  </td>
                </tr>
              ) : (
                displayTrades.map((trade, index) => (
                  <tr 
                    key={trade.id || index} 
                    className="hover:bg-gray-800 transition-colors"
                  >
                    {/* Time */}
                    <td className="px-3 py-2 text-sm text-gray-300 font-mono">
                      {formatTime(trade.timestamp)}
                    </td>

                    {/* Type */}
                    <td className="px-3 py-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                        trade.type === 'buy' 
                          ? 'bg-green-900 text-green-300 border border-green-700' 
                          : 'bg-red-900 text-red-300 border border-red-700'
                      }`}>
                        {trade.type.toUpperCase()}
                      </span>
                    </td>

                    {/* Price */}
                    <td className="px-3 py-2 text-sm text-white font-mono text-right">
                      ${formatPrice(trade.price)}
                    </td>

                    {/* Amount */}
                    <td className="px-3 py-2 text-sm text-gray-300 font-mono text-right">
                      {formatAmount(trade.amount)}
                    </td>

                    {/* Total SOL */}
                    <td className="px-3 py-2 text-sm text-white font-mono text-right">
                      ◎ {formatSOL(trade.totalSOL)}
                    </td>

                    {/* Trader */}
                    <td className="px-3 py-2 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={() => copyToClipboard(trade.wallet)}
                          className="text-xs text-blue-400 hover:text-blue-300 font-mono transition-colors"
                          title={`Click to copy: ${trade.wallet}`}
                        >
                          {truncateWallet(trade.wallet)}
                        </button>
                        {trade.txHash && trade.txHash !== 'unknown' && (
                          <a
                            href={`https://solscan.io/tx/${trade.txHash}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-gray-400 hover:text-white transition-colors"
                            title="View on Solscan"
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.25 5.5a.75.75 0 00-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 00.75-.75v-4a.75.75 0 011.5 0v4A2.25 2.25 0 0112.75 17h-8.5A2.25 2.25 0 012 14.75v-8.5A2.25 2.25 0 014.25 4h5a.75.75 0 010 1.5h-5z" clipRule="evenodd" />
                              <path fillRule="evenodd" d="M6.194 12.753a.75.75 0 001.06.053L16.5 4.44v2.81a.75.75 0 001.5 0v-4.5a.75.75 0 00-.75-.75h-4.5a.75.75 0 000 1.5h2.553l-9.056 8.194a.75.75 0 00-.053 1.06z" clipRule="evenodd" />
                            </svg>
                          </a>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Footer */}
      {displayTrades.length > 0 && (
        <div className="px-4 py-2 border-t border-gray-700 text-xs text-gray-500 text-center">
          Showing latest {displayTrades.length} trades • Updates in real-time
        </div>
      )}
    </div>
  );
};

export default TradesTable;
