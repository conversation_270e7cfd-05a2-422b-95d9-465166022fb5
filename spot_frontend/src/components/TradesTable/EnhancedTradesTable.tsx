import React, { useState, useEffect } from 'react';
import { useTradeData } from '../../hooks/useTradeData';

interface EnhancedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: number;
  price: number;
  valueUsd: number;
  tokenAmount: number;
  tokenAmountUsd: number;
  pair: string;
  blockchain: string;
  dex: string;
  wallet: string;
  txHash: string;
  // Enhanced Mobula fields
  solAmount: number;
  tokenSymbol: string;
  tokenName: string;
  exchange: string;
  marketCap: number;
  liquidity: number;
  volume24h: number;
  priceChange24h: number;
  displayAmount: string;
  displayPrice: string;
  displayValueUsd: string;
  displaySolAmount: string;
  displayMarketCap: string;
  displayVolume24h: string;
  displayPriceChange24h: string;
  timeAgo: string;
  totalSOL: number;
  totalUSD: number;
  tipAndPrio: {
    tip: number;
    priority: string;
  };
}

interface EnhancedTradesTableProps {
  poolAddress: string;
  className?: string;
  maxTrades?: number;
}

const EnhancedTradesTable: React.FC<EnhancedTradesTableProps> = ({ 
  poolAddress, 
  className = '',
  maxTrades = 100
}) => {
  const { trades, isLoading, error, isConnected } = useTradeData(poolAddress);
  const [displayTrades, setDisplayTrades] = useState<EnhancedTrade[]>([]);

  useEffect(() => {
    if (trades && trades.length > 0) {
      setDisplayTrades(trades.slice(0, maxTrades));
    }
  }, [trades, maxTrades]);

  const formatAge = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - (timestamp * 1000);
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    if (seconds > 0) return `${seconds}s`;
    return 'now';
  };

  const formatTipAndPrio = (trade: EnhancedTrade) => {
    const tip = trade.tipAndPrio?.tip || 0;
    const priority = trade.tipAndPrio?.priority || 'Low';
    
    return {
      tip: tip > 0 ? `${tip.toFixed(4)} SOL` : '0.0000 SOL',
      priority: priority
    };
  };

  const formatPrice = (price: number): string => {
    if (price === 0) return '$0.00';
    if (price < 0.000001) return `$${price.toExponential(2)}`;
    if (price < 0.01) return `$${price.toFixed(6)}`;
    return `$${price.toFixed(4)}`;
  };

  const formatAmount = (amount: number, symbol: string = 'SOL'): string => {
    if (amount === 0) return `0.00 ${symbol}`;
    if (amount < 0.01) return `${amount.toFixed(6)} ${symbol}`;
    if (amount >= 1000000) return `${(amount / 1000000).toFixed(2)}M ${symbol}`;
    if (amount >= 1000) return `${(amount / 1000).toFixed(2)}K ${symbol}`;
    return `${amount.toFixed(2)} ${symbol}`;
  };

  const formatUSD = (usd: number): string => {
    if (usd === 0) return '$0.00';
    if (usd < 0.01) return `$${usd.toFixed(6)}`;
    if (usd >= 1000000) return `$${(usd / 1000000).toFixed(2)}M`;
    if (usd >= 1000) return `$${(usd / 1000).toFixed(2)}K`;
    return `$${usd.toFixed(2)}`;
  };

  const truncateWallet = (wallet: string): string => {
    if (!wallet || wallet === 'unknown') return 'N/A';
    return `${wallet.slice(0, 3)}...${wallet.slice(-3)}`;
  };

  if (isLoading) {
    return (
      <div className={`bg-gray-950 border border-gray-800 ${className}`}>
        <div className="p-4 text-center text-gray-500 text-sm">
          Loading enhanced trade data...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-gray-950 border border-gray-800 ${className}`}>
        <div className="p-4 text-center text-red-400 text-sm">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-950 border border-gray-800 ${className}`}>
      {/* Header with tabs */}
      <div className="flex items-center border-b border-gray-800 bg-gray-900">
        <div className="flex">
          <button className="px-4 py-2 text-sm text-white bg-gray-800 border-r border-gray-700">
            Trades
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Positions
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Orders
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Holders
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Top Traders
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white">
            Dev Tokens
          </button>
        </div>
        <div className="ml-auto flex items-center px-4 py-2">
          <button className="text-blue-400 text-sm mr-4">Instant Trade</button>
          <span className="text-gray-400 text-xs mr-2">DEV</span>
          <span className="text-gray-400 text-xs mr-2">YOU</span>
          <button className="text-gray-400 text-xs">⚙️</button>
        </div>
      </div>

      {/* Live Data indicator */}
      <div className="px-4 py-1 bg-gray-900 border-b border-gray-800">
        <div className="flex items-center text-xs">
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-green-400">Live Data</span>
          </div>
          <div className="ml-auto text-gray-400">
            {displayTrades.length} trades • Enhanced Mobula Feed
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden">
        <table className="w-full text-xs">
          {/* Header */}
          <thead>
            <tr className="border-b border-gray-800 bg-gray-900">
              <th className="text-left px-3 py-2 text-gray-400 font-normal">Age</th>
              <th className="text-left px-3 py-2 text-gray-400 font-normal">Token</th>
              <th className="text-left px-3 py-2 text-gray-400 font-normal">Side</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Price $</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Amount</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Total USD</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Total SOL</th>
              <th className="text-center px-3 py-2 text-gray-400 font-normal">Exchange</th>
              <th className="text-center px-3 py-2 text-gray-400 font-normal">Txn</th>
            </tr>
          </thead>

          {/* Body */}
          <tbody>
            {displayTrades.length === 0 ? (
              <tr>
                <td colSpan={9} className="px-3 py-6 text-center text-gray-500">
                  No enhanced trade data available
                </td>
              </tr>
            ) : (
              displayTrades.map((trade, index) => (
                <tr 
                  key={trade.id || index} 
                  className="border-b border-gray-900 hover:bg-gray-900 transition-colors"
                >
                  {/* Age */}
                  <td className="px-3 py-2">
                    <div className="text-blue-400 text-xs">{formatAge(trade.timestamp)}</div>
                    <div className="text-gray-500 text-xs">Live</div>
                  </td>

                  {/* Token */}
                  <td className="px-3 py-2">
                    <div className="text-white text-xs font-medium">{trade.tokenSymbol}</div>
                    <div className="text-gray-400 text-xs truncate max-w-20" title={trade.tokenName}>
                      {trade.tokenName}
                    </div>
                  </td>

                  {/* Side */}
                  <td className="px-3 py-2">
                    <div className={`flex items-center text-xs ${
                      trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      <span className="mr-1">
                        {trade.type === 'buy' ? '↗' : '↘'}
                      </span>
                      {trade.type === 'buy' ? 'Buy' : 'Sell'}
                    </div>
                  </td>

                  {/* Price */}
                  <td className="px-3 py-2 text-right text-white font-mono text-xs">
                    {formatPrice(trade.price)}
                  </td>

                  {/* Amount */}
                  <td className="px-3 py-2 text-right text-white font-mono text-xs">
                    {formatAmount(trade.tokenAmount, trade.tokenSymbol)}
                  </td>

                  {/* Total USD */}
                  <td className="px-3 py-2 text-right text-white font-mono text-xs">
                    {formatUSD(trade.totalUSD)}
                  </td>

                  {/* Total SOL */}
                  <td className="px-3 py-2 text-right text-white font-mono text-xs">
                    {formatAmount(trade.solAmount, 'SOL')}
                  </td>

                  {/* Exchange */}
                  <td className="px-3 py-2 text-center">
                    <div className="text-blue-400 text-xs">{trade.exchange}</div>
                  </td>

                  {/* Transaction */}
                  <td className="px-3 py-2 text-center">
                    {trade.txHash && trade.txHash !== 'unknown' ? (
                      <a
                        href={`https://solscan.io/tx/${trade.txHash}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors"
                      >
                        View
                      </a>
                    ) : (
                      <span className="bg-gray-600 text-gray-300 px-2 py-1 rounded text-xs">
                        N/A
                      </span>
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Footer with additional info */}
      {displayTrades.length > 0 && (
        <div className="px-4 py-2 border-t border-gray-700 text-xs text-gray-500 flex justify-between">
          <span>Enhanced Mobula WebSocket Feed • Real-time updates</span>
          <span>{displayTrades.length} trades loaded</span>
        </div>
      )}
    </div>
  );
};

export default EnhancedTradesTable;
