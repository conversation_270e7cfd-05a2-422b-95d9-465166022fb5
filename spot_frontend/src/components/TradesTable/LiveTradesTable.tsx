import React, { useState, useEffect } from 'react';
import { useTradeData } from '../../hooks/useTradeData';

interface Trade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  price: number;
  amount: number;
  totalSOL: number;
  totalUSD: number;
  wallet: string;
  txHash: string;
  timeAgo: string;
  displayAmount?: string;
  displayPrice?: string;
  displayValueUsd?: string;
  tipAndPrio?: {
    tip: number;
    priority: string;
  };
}

interface LiveTradesTableProps {
  poolAddress: string;
  className?: string;
  maxTrades?: number;
}

const LiveTradesTable: React.FC<LiveTradesTableProps> = ({ 
  poolAddress, 
  className = '',
  maxTrades = 100
}) => {
  const { trades, isLoading, error, isConnected } = useTradeData(poolAddress);
  const [displayTrades, setDisplayTrades] = useState<Trade[]>([]);

  useEffect(() => {
    if (trades && trades.length > 0) {
      setDisplayTrades(trades.slice(0, maxTrades));
    }
  }, [trades, maxTrades]);

  const formatAge = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - (timestamp * 1000);
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    if (seconds > 0) return `${seconds}s`;
    return 'now';
  };

  const formatTipAndPrio = (trade: Trade) => {
    const tip = trade.tipAndPrio?.tip || 0;
    const priority = trade.tipAndPrio?.priority || 'Low';
    
    return {
      tip: tip > 0 ? `${tip.toFixed(4)} SOL` : '0.0000 SOL',
      priority: priority
    };
  };

  const formatPrice = (price: number): string => {
    if (price === 0) return '$0.00';
    if (price < 0.01) return `$${price.toFixed(6)}`;
    return `$${price.toFixed(2)}`;
  };

  const formatAmount = (amount: number): string => {
    if (amount === 0) return '0.00 SOL';
    if (amount < 0.01) return `${amount.toFixed(6)} SOL`;
    return `${amount.toFixed(2)} SOL`;
  };

  const formatTotalUSD = (usd: number): string => {
    if (usd === 0) return '$0.00';
    if (usd < 0.01) return `$${usd.toFixed(6)}`;
    return `$${usd.toFixed(2)}`;
  };

  const formatTotalSOL = (sol: number): string => {
    if (sol === 0) return '0.00 SOL';
    if (sol < 0.01) return `${sol.toFixed(6)} SOL`;
    return `${sol.toFixed(2)} SOL`;
  };

  const truncateWallet = (wallet: string): string => {
    if (!wallet || wallet === 'unknown') return 'N/A';
    return `${wallet.slice(0, 3)}...${wallet.slice(-3)}`;
  };

  if (isLoading) {
    return (
      <div className={`bg-gray-950 border border-gray-800 ${className}`}>
        <div className="p-4 text-center text-gray-500 text-sm">
          Loading trades...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-gray-950 border border-gray-800 ${className}`}>
        <div className="p-4 text-center text-red-400 text-sm">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-950 border border-gray-800 ${className}`}>
      {/* Header with tabs */}
      <div className="flex items-center border-b border-gray-800 bg-gray-900">
        <div className="flex">
          <button className="px-4 py-2 text-sm text-white bg-gray-800 border-r border-gray-700">
            Trades
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Positions
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Orders
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Holders
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white border-r border-gray-700">
            Top Traders
          </button>
          <button className="px-4 py-2 text-sm text-gray-400 hover:text-white">
            Dev Tokens
          </button>
        </div>
        <div className="ml-auto flex items-center px-4 py-2">
          <button className="text-blue-400 text-sm mr-4">Instant Trade</button>
          <span className="text-gray-400 text-xs mr-2">DEV</span>
          <span className="text-gray-400 text-xs mr-2">YOU</span>
          <button className="text-gray-400 text-xs">⚙️</button>
        </div>
      </div>

      {/* Live Data indicator */}
      <div className="px-4 py-1 bg-gray-900 border-b border-gray-800">
        <div className="flex items-center text-xs">
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="text-green-400">Live Data</span>
          </div>
          <div className="ml-auto text-gray-400">
            {displayTrades.length} trades
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-hidden">
        <table className="w-full text-xs">
          {/* Header */}
          <thead>
            <tr className="border-b border-gray-800 bg-gray-900">
              <th className="text-left px-3 py-2 text-gray-400 font-normal">Age</th>
              <th className="text-left px-3 py-2 text-gray-400 font-normal">Tip & Prio</th>
              <th className="text-left px-3 py-2 text-gray-400 font-normal">Side</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Price $</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Amount</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Total USD</th>
              <th className="text-right px-3 py-2 text-gray-400 font-normal">Total SOL</th>
              <th className="text-center px-3 py-2 text-gray-400 font-normal">Maker Txn</th>
            </tr>
          </thead>

          {/* Body */}
          <tbody>
            {displayTrades.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-3 py-6 text-center text-gray-500">
                  No trades available
                </td>
              </tr>
            ) : (
              displayTrades.map((trade, index) => {
                const tipPrio = formatTipAndPrio(trade);
                return (
                  <tr 
                    key={trade.id || index} 
                    className="border-b border-gray-900 hover:bg-gray-900 transition-colors"
                  >
                    {/* Age */}
                    <td className="px-3 py-2">
                      <div className="text-blue-400 text-xs">{formatAge(trade.timestamp)}</div>
                      <div className="text-gray-500 text-xs">Other</div>
                    </td>

                    {/* Tip & Prio */}
                    <td className="px-3 py-2">
                      <div className="text-white text-xs">{tipPrio.tip}</div>
                      <div className="text-green-400 text-xs">{tipPrio.priority}</div>
                    </td>

                    {/* Side */}
                    <td className="px-3 py-2">
                      <div className={`flex items-center text-xs ${
                        trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                      }`}>
                        <span className="mr-1">
                          {trade.type === 'buy' ? '↗' : '↘'}
                        </span>
                        {trade.type === 'buy' ? 'Buy' : 'Sell'}
                      </div>
                    </td>

                    {/* Price */}
                    <td className="px-3 py-2 text-right text-white font-mono text-xs">
                      {formatPrice(trade.price)}
                    </td>

                    {/* Amount */}
                    <td className="px-3 py-2 text-right text-white font-mono text-xs">
                      {formatAmount(trade.amount)}
                    </td>

                    {/* Total USD */}
                    <td className="px-3 py-2 text-right text-white font-mono text-xs">
                      {formatTotalUSD(trade.totalUSD)}
                    </td>

                    {/* Total SOL */}
                    <td className="px-3 py-2 text-right text-white font-mono text-xs">
                      {formatTotalSOL(trade.totalSOL)}
                    </td>

                    {/* Maker Txn */}
                    <td className="px-3 py-2 text-center">
                      {trade.txHash && trade.txHash !== 'unknown' ? (
                        <a
                          href={`https://solscan.io/tx/${trade.txHash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors"
                        >
                          View
                        </a>
                      ) : (
                        <span className="bg-gray-600 text-gray-300 px-2 py-1 rounded text-xs">
                          N/A
                        </span>
                      )}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default LiveTradesTable;
