import React, { useState, useEffect } from 'react';
import { useTradeData } from '../hooks/useTradeData';

const TradeDataDebug: React.FC = () => {
  const [poolAddress, setPoolAddress] = useState('3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe');
  const { trades, isLoading, isConnected, error, lastUpdate } = useTradeData(poolAddress);

  const handlePoolAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPoolAddress(e.target.value);
  };

  return (
    <div className="bg-gray-900 p-6 rounded-lg border border-gray-700 max-w-4xl mx-auto">
      <h2 className="text-xl font-bold text-white mb-4">🔍 Trade Data Debug</h2>
      
      {/* Pool Address Input */}
      <div className="mb-4">
        <label className="block text-gray-300 text-sm font-medium mb-2">
          Pool Address:
        </label>
        <input
          type="text"
          value={poolAddress}
          onChange={handlePoolAddressChange}
          className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white text-sm font-mono"
          placeholder="Enter pool address..."
        />
      </div>

      {/* Status */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-800 p-3 rounded">
          <div className="text-gray-400 text-xs">Status</div>
          <div className={`text-sm font-medium ${isLoading ? 'text-yellow-400' : 'text-green-400'}`}>
            {isLoading ? 'Loading...' : 'Ready'}
          </div>
        </div>
        
        <div className="bg-gray-800 p-3 rounded">
          <div className="text-gray-400 text-xs">Connection</div>
          <div className={`text-sm font-medium ${isConnected ? 'text-green-400' : 'text-red-400'}`}>
            {isConnected ? 'Connected' : 'Disconnected'}
          </div>
        </div>
        
        <div className="bg-gray-800 p-3 rounded">
          <div className="text-gray-400 text-xs">Trades Count</div>
          <div className="text-white text-sm font-medium">{trades.length}</div>
        </div>
        
        <div className="bg-gray-800 p-3 rounded">
          <div className="text-gray-400 text-xs">Last Update</div>
          <div className="text-white text-sm font-medium">
            {lastUpdate ? new Date(lastUpdate).toLocaleTimeString() : 'Never'}
          </div>
        </div>
      </div>

      {/* Error */}
      {error && (
        <div className="bg-red-900 border border-red-700 p-3 rounded mb-4">
          <div className="text-red-300 text-sm">
            <strong>Error:</strong> {error}
          </div>
        </div>
      )}

      {/* Trades Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-700">
          <h3 className="text-white font-medium">Recent Trades</h3>
        </div>
        
        {trades.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No trades available
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left px-4 py-3 text-gray-400 font-medium">Type</th>
                  <th className="text-right px-4 py-3 text-gray-400 font-medium">Amount</th>
                  <th className="text-right px-4 py-3 text-gray-400 font-medium">Price</th>
                  <th className="text-right px-4 py-3 text-gray-400 font-medium">USD Value</th>
                  <th className="text-right px-4 py-3 text-gray-400 font-medium">Age</th>
                </tr>
              </thead>
              <tbody>
                {trades.slice(0, 10).map((trade, index) => (
                  <tr key={trade.id || index} className="border-b border-gray-700 hover:bg-gray-750">
                    <td className="px-4 py-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                        trade.type === 'buy' 
                          ? 'bg-green-900 text-green-300' 
                          : 'bg-red-900 text-red-300'
                      }`}>
                        {trade.type === 'buy' ? '↗ Buy' : '↘ Sell'}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-right text-white font-mono">
                      {trade.amount.toFixed(2)}
                    </td>
                    <td className="px-4 py-3 text-right text-white font-mono">
                      ${trade.price < 0.001 ? trade.price.toExponential(3) : trade.price.toFixed(6)}
                    </td>
                    <td className="px-4 py-3 text-right text-white font-mono">
                      ${trade.usdAmount.toFixed(2)}
                    </td>
                    <td className="px-4 py-3 text-right text-gray-400">
                      {trade.age}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Raw Data (for debugging) */}
      <details className="mt-6">
        <summary className="text-gray-400 cursor-pointer hover:text-white">
          🔧 Raw Trade Data (Click to expand)
        </summary>
        <pre className="mt-2 p-4 bg-gray-800 rounded text-xs text-gray-300 overflow-auto max-h-96">
          {JSON.stringify(trades.slice(0, 3), null, 2)}
        </pre>
      </details>
    </div>
  );
};

export default TradeDataDebug;
