import React, { useState } from 'react';
import TradesTable from '../components/TradesTable/TradesTable';
import CompactTradesTable from '../components/TradesTable/CompactTradesTable';
import ProfessionalTradesTable from '../components/TradesTable/ProfessionalTradesTable';
import LiveTradesTable from '../components/TradesTable/LiveTradesTable';
import EnhancedTradesTable from '../components/TradesTable/EnhancedTradesTable';
import TradeDataTest from '../components/TradeDataTest';
import TradeDataDebug from '../components/TradeDataDebug';

const TradesDemo: React.FC = () => {
  const [poolAddress, setPoolAddress] = useState('3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe');
  const [activeTab, setActiveTab] = useState<'debug' | 'enhanced' | 'professional' | 'live' | 'compact' | 'full' | 'test'>('debug');

  const handlePoolAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPoolAddress(e.target.value);
  };

  return (
    <div className="min-h-screen bg-gray-950 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-white">
            🚀 Trades Table Demo
          </h1>
          <p className="text-gray-400">
            Real-time trade data with WebSocket-first loading
          </p>
        </div>

        {/* Pool Address Input */}
        <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Pool Address
          </label>
          <input
            type="text"
            value={poolAddress}
            onChange={handlePoolAddressChange}
            className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter Solana pool address..."
          />
          <p className="mt-2 text-xs text-gray-500">
            Enter a valid Solana pool address to load trade data
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-900 p-1 rounded-lg border border-gray-700">
          <button
            onClick={() => setActiveTab('debug')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'debug'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            🔍 Debug
          </button>
          <button
            onClick={() => setActiveTab('enhanced')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'enhanced'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            🚀 Enhanced
          </button>
          <button
            onClick={() => setActiveTab('professional')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'professional'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            🎯 Professional
          </button>
          <button
            onClick={() => setActiveTab('live')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'live'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            📡 Live Data
          </button>
          <button
            onClick={() => setActiveTab('compact')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'compact'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            📊 Compact
          </button>
          <button
            onClick={() => setActiveTab('full')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'full'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            📋 Full
          </button>
          <button
            onClick={() => setActiveTab('test')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'test'
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'
            }`}
          >
            🧪 Test
          </button>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {activeTab === 'debug' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  🔍 Trade Data Debug Tool
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Debug and test trade data loading from the API and WebSocket. Check connection status, view raw data, and test different pool addresses.
                </p>
              </div>
              <TradeDataDebug />
            </div>
          )}

          {activeTab === 'enhanced' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  Enhanced Mobula Trades Table
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Full integration with Mobula WebSocket feed showing token details, exchange info, and rich trade data.
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-blue-400 font-medium">Token Info:</span>
                    <p className="text-gray-300">Symbol, name, market cap</p>
                  </div>
                  <div>
                    <span className="text-green-400 font-medium">Exchange:</span>
                    <p className="text-gray-300">DEX name and type</p>
                  </div>
                  <div>
                    <span className="text-purple-400 font-medium">Amounts:</span>
                    <p className="text-gray-300">Token, SOL, USD values</p>
                  </div>
                  <div>
                    <span className="text-yellow-400 font-medium">Real-time:</span>
                    <p className="text-gray-300">Live WebSocket updates</p>
                  </div>
                </div>
              </div>
              <EnhancedTradesTable
                poolAddress={poolAddress}
                className="w-full"
                maxTrades={50}
              />
            </div>
          )}

          {activeTab === 'professional' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  Professional Trades Table
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Exact replica of your screenshot with tabs, live data indicator, and professional styling.
                </p>
              </div>
              <ProfessionalTradesTable
                poolAddress={poolAddress}
                className="w-full"
                maxTrades={50}
              />
            </div>
          )}

          {activeTab === 'live' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  Live Trades Table
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Professional styling with real WebSocket data integration and live updates.
                </p>
              </div>
              <LiveTradesTable
                poolAddress={poolAddress}
                className="w-full"
                maxTrades={50}
              />
            </div>
          )}

          {activeTab === 'compact' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  Compact Trades Table
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Matches the style from your screenshot with compact rows and efficient space usage.
                </p>
              </div>
              <CompactTradesTable 
                poolAddress={poolAddress}
                className="w-full"
                maxTrades={50}
              />
            </div>
          )}

          {activeTab === 'full' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  Full Trades Table
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  More detailed view with larger rows and additional information.
                </p>
              </div>
              <TradesTable 
                poolAddress={poolAddress}
                className="w-full"
              />
            </div>
          )}

          {activeTab === 'test' && (
            <div className="space-y-4">
              <div className="bg-gray-900 rounded-lg border border-gray-700 p-4">
                <h2 className="text-lg font-semibold text-white mb-2">
                  WebSocket Test
                </h2>
                <p className="text-gray-400 text-sm mb-4">
                  Test the WebSocket-first data loading functionality.
                </p>
              </div>
              <TradeDataTest poolAddress={poolAddress} />
            </div>
          )}
        </div>

        {/* Features Info */}
        <div className="bg-gray-900 rounded-lg border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            ✨ Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium text-blue-400">🚀 WebSocket-First Loading</h4>
              <p className="text-gray-400">
                Tries WebSocket cache first, then falls back to API for fastest loading
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-400">📡 Real-time Updates</h4>
              <p className="text-gray-400">
                Live trade data updates via WebSocket connection
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-purple-400">🎨 Multiple Styles</h4>
              <p className="text-gray-400">
                Compact and full table views to match your design needs
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-yellow-400">🔗 Transaction Links</h4>
              <p className="text-gray-400">
                Click trader addresses to copy, view transactions on Solscan
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-red-400">📊 Smart Formatting</h4>
              <p className="text-gray-400">
                Intelligent number formatting for prices, amounts, and time
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-indigo-400">⚡ Performance</h4>
              <p className="text-gray-400">
                Optimized rendering with configurable trade limits
              </p>
            </div>
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="bg-gray-900 rounded-lg border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            📖 Usage
          </h3>
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="font-medium text-white mb-2">Import the component:</h4>
              <pre className="bg-gray-800 p-3 rounded text-green-400 overflow-x-auto">
{`import CompactTradesTable from './components/TradesTable/CompactTradesTable';

<CompactTradesTable 
  poolAddress="your-pool-address"
  maxTrades={50}
  className="w-full"
/>`}
              </pre>
            </div>
            <div>
              <h4 className="font-medium text-white mb-2">Key Props:</h4>
              <ul className="text-gray-400 space-y-1 ml-4">
                <li>• <code className="text-blue-400">poolAddress</code>: Solana pool address to monitor</li>
                <li>• <code className="text-blue-400">maxTrades</code>: Maximum number of trades to display</li>
                <li>• <code className="text-blue-400">className</code>: Additional CSS classes</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradesDemo;
