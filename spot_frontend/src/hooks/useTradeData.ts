import { useState, useEffect, useCallback, useRef } from 'react';
import { websocketService } from '@/services/websocketService';
import {
  formatSmallNumber,
  formatPrice,
  formatLargeNumber,
  formatCurrency,
  formatTimeAgo,
  formatWalletAddress,
  getPriorityLevel,
  formatSOL,
  formatTxHash
} from '@/utils/numberFormatting';

interface TradeDataUpdate {
  poolAddress: string;
  tradeData: any;
  tradeHistory: any[];
  timestamp: number;
}

interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  side: 'Buy' | 'Sell';
  amount: string;
  usdAmount: string;
  price: string;
  priceSOL: number;
  mc: string;
  trader: string;
  age: string;
  displayAmount: string;
  displayPrice: string;
  displayValueUsd: string;
  timeAgo: string;
  txHash: string;
  wallet: string;
  makerTxn: string;
  totalUSD: number;
  totalSOL: number;
  marketCap: number; // Raw market cap value for formatting
  tokenAmount: number; // Raw token amount for formatting
  tipAndPrio: { tip: number; priority: 'High' | 'Medium' | 'Low' };
}

interface UseTradeDataReturn {
  trades: FormattedTrade[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  lastUpdate: number | null;
  refetch: () => Promise<void>;
}

export const useTradeData = (poolAddress: string | null): UseTradeDataReturn => {
  const [trades, setTrades] = useState<FormattedTrade[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<number | null>(null);
  
  const currentPoolAddress = useRef<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Format trade data for display
  const formatTrade = useCallback((trade: any): FormattedTrade => {

    const priceValue = trade.price || 0;
    const amountValue = trade.tokenAmount || trade.amount || 0;
    const usdValue = trade.tokenAmountUsd || trade.value_usd || 0;

    // Get market cap from backend data or calculate estimate
    let marketCapValue = 0;
    if (trade.marketCap) {
      marketCapValue = trade.marketCap;
    } else if (trade.displayMarketCap) {
      // Remove currency formatting and parse
      marketCapValue = parseFloat(trade.displayMarketCap.toString().replace(/[$,]/g, '')) || 0;
    } else {
      // Fallback calculation (price * estimated supply)
      marketCapValue = priceValue * 1000000; // Rough estimate
    }

    return {
      id: trade.id || `${trade.timestamp}-${Math.random()}`,
      timestamp: trade.timestamp,
      type: trade.type,
      side: trade.type === 'buy' ? 'Buy' : 'Sell',
      amount: formatLargeNumber(amountValue),
      usdAmount: formatCurrency(usdValue),
      price: formatPrice(priceValue),
      priceSOL: priceValue / 144.67, // Convert to SOL price (approximate)
      mc: formatCurrency(marketCapValue), // Use actual market cap data
      trader: formatWalletAddress(trade.wallet),
      age: formatTimeAgo(trade.timestamp),
      displayAmount: trade.displayAmount || formatLargeNumber(amountValue),
      displayPrice: trade.displayPrice || formatPrice(priceValue),
      displayValueUsd: trade.displayValueUsd || formatCurrency(usdValue),
      timeAgo: trade.timeAgo || formatTimeAgo(trade.timestamp),
      txHash: trade.txHash || trade.tx_hash || '',
      wallet: trade.wallet || '',
      makerTxn: formatTxHash(trade.txHash || trade.tx_hash || ''),
      totalUSD: usdValue,
      totalSOL: trade.totalSOL || (usdValue / (trade.solPrice || 144.67)), // Use real SOL price if available
      marketCap: marketCapValue, // Add raw market cap value for formatting
      tokenAmount: amountValue, // Add raw token amount for formatting
      tipAndPrio: {
        tip: trade.tip || 0, // Use real tip data if available
        priority: getPriorityLevel(usdValue)
      }
    };
  }, []);

  // Fetch initial trade data from API
  const fetchInitialTradeData = useCallback(async (poolAddr: string) => {
    if (!poolAddr) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log(`📊 Fetching initial trade data for pool: ${poolAddr}`);
      
      const response = await fetch(`/api/trade/initial/${poolAddr}?limit=20`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch trade data: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.data.trades) {
        const formattedTrades = data.data.trades.map(formatTrade);
        setTrades(formattedTrades);
        setLastUpdate(Date.now());

        const source = data.data.source || 'unknown';
        console.log(`✅ Loaded ${formattedTrades.length} initial trades (source: ${source})`);
      } else {
        console.warn('No trade data received from API');
        setTrades([]);
      }
    } catch (err: any) {
      console.error('❌ Failed to fetch initial trade data:', err);
      setError(err.message);
      setTrades([]);
    } finally {
      setIsLoading(false);
    }
  }, [formatTrade]);

  // Handle real-time trade data updates
  const handleTradeDataUpdate = useCallback((update: TradeDataUpdate) => {
    if (update.poolAddress !== currentPoolAddress.current) {
      return; // Ignore updates for different pools
    }

    console.log(`📊 Processing trade update for pool: ${update.poolAddress}`);
    
    if (update.tradeHistory && update.tradeHistory.length > 0) {
      const formattedTrades = update.tradeHistory.map(formatTrade);
      setTrades(formattedTrades);
      setLastUpdate(update.timestamp);
      setError(null);
    }
  }, [formatTrade]);

  // Subscribe to WebSocket trade data
  const subscribeToTradeData = useCallback(async (poolAddr: string) => {
    try {
      console.log(`📡 Subscribing to trade WebSocket for pool: ${poolAddr}`);
      
      // Subscribe to trade data updates
      const unsubscribe = websocketService.onTradeData(handleTradeDataUpdate);
      unsubscribeRef.current = unsubscribe;

      // Subscribe to the specific pool
      await websocketService.subscribeToTradeData(poolAddr);
      
      setIsConnected(true);
      console.log(`✅ Successfully subscribed to trade data for pool: ${poolAddr}`);
    } catch (err: any) {
      console.error('❌ Failed to subscribe to trade data:', err);
      setError(err.message);
      setIsConnected(false);
    }
  }, [handleTradeDataUpdate]);

  // Unsubscribe from WebSocket trade data
  const unsubscribeFromTradeData = useCallback((poolAddr: string) => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    if (poolAddr) {
      websocketService.unsubscribeFromTradeData(poolAddr);
      console.log(`📡 Unsubscribed from trade data for pool: ${poolAddr}`);
    }

    setIsConnected(false);
  }, []);

  // Refetch data manually
  const refetch = useCallback(async () => {
    if (currentPoolAddress.current) {
      await fetchInitialTradeData(currentPoolAddress.current);
    }
  }, [fetchInitialTradeData]);

  // Main effect to handle pool address changes
  useEffect(() => {
    // Cleanup previous subscription
    if (currentPoolAddress.current) {
      unsubscribeFromTradeData(currentPoolAddress.current);
    }

    // Reset state
    setTrades([]);
    setError(null);
    setLastUpdate(null);
    setIsConnected(false);

    // Set new pool address
    currentPoolAddress.current = poolAddress;

    // Subscribe to new pool if provided
    if (poolAddress) {
      console.log(`🔄 Pool address changed to: ${poolAddress}`);
      
      // Fetch initial data first
      fetchInitialTradeData(poolAddress).then(() => {
        // Then subscribe to real-time updates
        subscribeToTradeData(poolAddress);
      });
    }

    // Cleanup on unmount or pool change
    return () => {
      if (currentPoolAddress.current) {
        unsubscribeFromTradeData(currentPoolAddress.current);
      }
    };
  }, [poolAddress, fetchInitialTradeData, subscribeToTradeData, unsubscribeFromTradeData]);

  // Fetch trade data from WebSocket cache only
  const fetchWebSocketTradeData = useCallback(async (poolAddr: string) => {
    if (!poolAddr) return [];

    try {
      console.log(`📡 Fetching WebSocket trade data for pool: ${poolAddr}`);

      const response = await fetch(`/api/trade/websocket/${poolAddr}?limit=20`);

      if (!response.ok) {
        throw new Error(`Failed to fetch WebSocket trade data: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data.trades) {
        const formattedTrades = data.data.trades.map(formatTrade);
        console.log(`✅ Loaded ${formattedTrades.length} trades from WebSocket cache (cache size: ${data.data.cacheSize})`);
        return formattedTrades;
      } else {
        console.warn('⚠️ No WebSocket trade data available');
        return [];
      }
    } catch (err: any) {
      console.error('❌ Error fetching WebSocket trade data:', err);
      return [];
    }
  }, [formatTrade]);

  return {
    trades,
    isLoading,
    isConnected,
    error,
    lastUpdate,
    refetch,
    fetchWebSocketTradeData
  };
};
