import React, { useState, useEffect } from 'react';
import { ChevronUp, ChevronDown, Filter, User } from 'lucide-react';
import { useTradeData } from '@/hooks/useTradeData';
import { formatSmallNumber, formatCurrency, formatTimeAgo } from '@/utils/numberFormatting';

const Trades = () => {
  const [activeTab, setActiveTab] = useState('TRADES');
  const [sortAsc, setSortAsc] = useState(false); // Default to newest first
  const [poolAddress, setPoolAddress] = useState<string | null>(null);

  // Get trade data using the custom hook
  const { trades, isLoading, isConnected, error, lastUpdate } = useTradeData(poolAddress);

  // Debug trade data
  useEffect(() => {
    console.log('📊 Trades: Hook data update:', {
      poolAddress,
      tradesCount: trades.length,
      isLoading,
      isConnected,
      error,
      lastUpdate: lastUpdate ? new Date(lastUpdate).toLocaleTimeString() : null
    });
  }, [poolAddress, trades, isLoading, isConnected, error, lastUpdate]);

  // Get pool address from localStorage on component mount
  useEffect(() => {
    const activePulseToken = localStorage.getItem('activePulseToken');
    if (activePulseToken) {
      try {
        const tokenData = JSON.parse(activePulseToken);
        console.log('📊 Trades: Token data from localStorage:', tokenData);

        // Try multiple fields for pool address
        const poolAddr = tokenData.pool_address ||
                         tokenData.address ||
                         tokenData.id ||
                         tokenData.contract ||
                         tokenData.pair_address;

        if (poolAddr) {
          setPoolAddress(poolAddr);
          console.log('📊 Trades: Using pool address:', poolAddr);
          console.log('📊 Trades: Token symbol:', tokenData.symbol);
          console.log('📊 Trades: Token name:', tokenData.name);
        } else {
          console.warn('📊 Trades: No pool address found in token data:', Object.keys(tokenData));
          // Use a default test pool address for development
          const testPoolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe';
          setPoolAddress(testPoolAddress);
          console.log('📊 Trades: Using test pool address:', testPoolAddress);
        }
      } catch (error) {
        console.error('📊 Trades: Failed to parse activePulseToken:', error);
        // Use a default test pool address for development
        const testPoolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe';
        setPoolAddress(testPoolAddress);
        console.log('📊 Trades: Using test pool address after error:', testPoolAddress);
      }
    } else {
      console.warn('📊 Trades: No activePulseToken found in localStorage');
      // Use a default test pool address for development
      const testPoolAddress = '3Sh7S9XwatY5aUCuufpzGrjT4PiqzMS6psuBpDcXKXXe';
      setPoolAddress(testPoolAddress);
      console.log('📊 Trades: Using test pool address (no localStorage):', testPoolAddress);
    }
  }, []);

  // Format market cap from trade data (matching trade_example.md logic)
  const formatMarketCap = (trade: any): string => {
    // Try to get market cap from different possible locations in the data structure
    let marketCap = 0;

    if (trade.marketCap) {
      marketCap = trade.marketCap;
    } else if (trade.mc) {
      // Remove $ and convert back to number if it's a formatted string
      const mcStr = trade.mc.toString().replace(/[$,]/g, '');
      marketCap = parseFloat(mcStr) || 0;
    }

    // Format market cap with appropriate suffix (K, M, B, T)
    if (marketCap >= 1e12) {
      return (marketCap / 1e12).toFixed(2) + 'T';
    } else if (marketCap >= 1e9) {
      return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
      return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
      return (marketCap / 1e3).toFixed(2) + 'K';
    } else {
      return marketCap.toFixed(0);
    }
  };

  // Format token amount from trade data (matching trade_example.md logic)
  const formatTokenAmount = (trade: any): string => {
    // Try to get token amount from different possible locations
    let tokenAmount = 0;
    let symbol = 'TOKEN';

    if (trade.tokenAmount) {
      tokenAmount = parseFloat(trade.tokenAmount.toString().replace(/[,]/g, '')) || 0;
    } else if (trade.amount) {
      tokenAmount = parseFloat(trade.amount.toString().replace(/[,]/g, '')) || 0;
    }

    // Format amount with appropriate suffix (K, M, B, T)
    let formattedAmount = '';
    if (tokenAmount >= 1e12) {
      formattedAmount = (tokenAmount / 1e12).toFixed(2) + 'T';
    } else if (tokenAmount >= 1e9) {
      formattedAmount = (tokenAmount / 1e9).toFixed(2) + 'B';
    } else if (tokenAmount >= 1e6) {
      formattedAmount = (tokenAmount / 1e6).toFixed(2) + 'M';
    } else if (tokenAmount >= 1e3) {
      formattedAmount = (tokenAmount / 1e3).toFixed(2) + 'K';
    } else {
      // For small numbers, check for zeros after decimal point
      const amountStr = tokenAmount.toString();
      if (amountStr.includes('.')) {
        const parts = amountStr.split('.');
        const integerPart = parts[0];
        const decimalPart = parts[1];

        // Count leading zeros in decimal part
        let leadingZeros = 0;
        for (let i = 0; i < decimalPart.length; i++) {
          if (decimalPart[i] === '0') {
            leadingZeros++;
          } else {
            break;
          }
        }

        // If we have 3 or more leading zeros, format with subscript notation
        if (leadingZeros >= 3) {
          const remainingDigits = decimalPart.substring(leadingZeros, leadingZeros + 2);
          formattedAmount = `${integerPart}.0${leadingZeros}${remainingDigits}`;
        } else {
          formattedAmount = parseFloat(tokenAmount).toFixed(2);
        }
      } else {
        formattedAmount = parseFloat(tokenAmount).toFixed(2);
      }
    }

    return `${formattedAmount} ${symbol}`;
  };

  // Format trade USD value (matching trade_example.md logic)
  const formatTradeValue = (trade: any): string => {
    // Try to get USD value from different possible locations
    let usdValue = 0;

    if (trade.totalUSD) {
      usdValue = trade.totalUSD;
    } else if (trade.tokenAmountUsd) {
      usdValue = parseFloat(trade.tokenAmountUsd.toString().replace(/[$,]/g, '')) || 0;
    } else if (trade.usdAmount) {
      usdValue = parseFloat(trade.usdAmount.toString().replace(/[$,]/g, '')) || 0;
    } else if (trade.displayValueUsd) {
      usdValue = parseFloat(trade.displayValueUsd.toString().replace(/[$,]/g, '')) || 0;
    }

    return formatSmallNumber(usdValue);
  };

  // Format transaction hash (matching trade_example.md logic)
  const formatHash = (trade: any): string => {
    // Try to get hash from different possible locations
    let hash = '';

    if (trade.txHash) {
      hash = trade.txHash;
    } else if (trade.makerTxn && trade.makerTxn !== 'N/A') {
      hash = trade.makerTxn;
    }

    // If no hash found
    if (!hash || hash === 'N/A') {
      return 'N/A';
    }

    // Format hash to show first 4 and last 4 characters
    if (hash.length > 8) {
      const shortHash = `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;

      // Create explorer link for Solana (default to Solscan)
      const explorerUrl = `https://solscan.io/tx/${hash}`;

      return `<a href="${explorerUrl}" target="_blank" style="color: #3498db; text-decoration: none;">${shortHash}</a>`;
    }

    return hash;
  };

  // Format age (matching trade_example.md logic)
  const formatAge = (timestamp: number): string => {
    const now = Date.now();
    const diffSeconds = Math.floor((now - (timestamp * 1000)) / 1000);

    if (diffSeconds < 5) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  const getCurrentData = () => {
    let data = trades;

    if (activeTab === 'DEV') {
      // Filter trades by dev wallets - this would need real dev wallet identification
      data = trades.filter(trade => trade.trader === 'Dev');
    } else if (activeTab === 'YOU') {
      // Filter trades by user's wallet - this would need user wallet identification
      data = trades.filter(trade => trade.trader === 'You');
    }

    // Sort by timestamp (age)
    return [...data].sort((a, b) => {
      return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    });
  };

  return (
    <div className="text-white max-h-[100rem] font-mono text-sm">
      {/* Header Tabs */}
      <div className="flex items-center justify-between p-4 border-b border-gray-800">
        {/* Connection Status Indicator */}
        {activeTab === 'TRADES' && (
          <div className="flex items-center space-x-2 text-xs">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">
              {isLoading ? 'Loading...' : isConnected ? `Live • ${trades.length} trades` : 'Disconnected'}
            </span>
            {error && <span className="text-red-400">Error: {error}</span>}
          </div>
        )}

        {/* Left side: TRADES */}
        <div>
          <button
            onClick={() => setActiveTab('TRADES')}
            className={`text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'TRADES' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            Trades
          </button>
        </div>

        {/* Right side: DEV and YOU */}
        <div className="flex space-x-6">
          <button
            onClick={() => setActiveTab('DEV')}
            className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'DEV' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            <Filter size={18} className="mr-1" />
            DEV
          </button>

          <button
            onClick={() => setActiveTab('YOU')}
            className={`flex items-center text-lg font-bold tracking-wide transition-colors ${
              activeTab === 'YOU' ? 'text-white' : 'text-gray-500 hover:text-gray-300'
            }`}
          >
            <User size={18} className="mr-1" />
            YOU
          </button>
        </div>
      </div>

      {/* Table Structure - Matching trade_example.md */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-800 text-gray-400 text-xs">
              <th className="text-left px-4 py-3">
                <div
                  className="flex items-center cursor-pointer hover:text-white transition-colors"
                  onClick={() => setSortAsc(!sortAsc)}
                >
                  <span>Age</span>
                  {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
                </div>
              </th>
              <th className="text-left px-4 py-3">Type</th>
              <th className="text-left px-4 py-3">Market Cap</th>
              <th className="text-left px-4 py-3">Amount</th>
              <th className="text-left px-4 py-3">USD Value</th>
              <th className="text-left px-4 py-3">Hash</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-800">
            {activeTab === 'TRADES' && isLoading && (
              <tr>
                <td colSpan={6} className="text-center py-8">
                  <div className="text-gray-400">Loading trade data...</div>
                </td>
              </tr>
            )}

            {activeTab === 'TRADES' && !isLoading && getCurrentData().length === 0 && (
              <tr>
                <td colSpan={6} className="text-center py-8">
                  <div className="text-gray-400">
                    {error ? 'Failed to load trade data' : 'No trades available'}
                  </div>
                </td>
              </tr>
            )}

            {getCurrentData().map((trade, index) => (
              <tr key={trade.id || index} className="hover:bg-gray-800/50 transition-colors">
                {/* Age */}
                <td className="px-4 py-2 text-gray-300 font-mono text-sm">
                  {formatAge(trade.timestamp)}
                </td>

                {/* Type */}
                <td className="px-4 py-2">
                  <span className={`font-medium ${
                    trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {trade.type}
                  </span>
                </td>

                {/* Market Cap */}
                <td className="px-4 py-2 text-gray-300 font-mono">
                  ${formatMarketCap(trade)}
                </td>

                {/* Amount */}
                <td className="px-4 py-2 text-gray-300 font-mono">
                  {formatTokenAmount(trade)}
                </td>

                {/* USD Value */}
                <td className="px-4 py-2 text-gray-300 font-mono">
                  ${formatTradeValue(trade)}
                </td>

                {/* Hash */}
                <td className="px-4 py-2 text-blue-400">
                  <div
                    dangerouslySetInnerHTML={{ __html: formatHash(trade) }}
                    className="font-mono text-sm"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

    </div>
  );
};

export default Trades;
