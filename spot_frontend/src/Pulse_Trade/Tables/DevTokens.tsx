import { useState, useEffect } from 'react';
import { ChevronDown, Copy, ExternalLink } from 'lucide-react';
import { homeAPI } from '../../utils/api';

interface DevTokenData {
  id: string;
  token: string;
  logo: string;
  age: string;
  marketCap: number;
  remaining: { amount: number; percentage: number };
  holders: number;
  devPnl: number | null;
  bondingProgress: number;
  contractAddress: string;
}

export default function DevTokens() {
  const [sortConfig, setSortConfig] = useState({ key: 'marketCap', direction: 'desc' });
  const [devTokensData, setDevTokensData] = useState<DevTokenData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch real dev tokens data
  useEffect(() => {
    const fetchDevTokensData = async () => {
      try {
        setLoading(true);
        setError(null);

        // For now, we'll use pulse data as dev tokens data
        // In a real implementation, you would have a specific API endpoint for dev tokens
        const pulseData = await homeAPI.getPulseData();

        if (pulseData && pulseData.length > 0) {
          const formattedTokens: DevTokenData[] = pulseData.map((token, index) => ({
            id: token.id || index.toString(),
            token: token.symbol || 'UNKNOWN',
            logo: token.imageUrl ? '🪙' : '💎', // Use emoji as fallback
            age: calculateAge(token.created_at || Date.now()),
            marketCap: token.market_cap || 0,
            remaining: {
              amount: token.bonding_percent ? (100 - token.bonding_percent) : 0,
              percentage: token.bonding_percent ? (100 - token.bonding_percent) : 0
            },
            holders: token.holders_count || 0,
            devPnl: null, // This would need to be calculated from real data
            bondingProgress: token.bonding_percent || 0,
            contractAddress: token.address || token.pool_address || 'N/A'
          }));

          setDevTokensData(formattedTokens);
        } else {
          setDevTokensData([]);
        }
      } catch (err) {
        console.error('Error fetching dev tokens data:', err);
        setError('Failed to load dev tokens data');
        setDevTokensData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDevTokensData();
  }, []);

  const calculateAge = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return '<1h';
    }
  };

  const formatCurrency = (value: number) => {
    if (value >= 1e12) return `$${(value / 1e12).toFixed(2)}T`;
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  const formatNumber = (value: number) => {
    if (value >= 1e6) return `${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `${(value / 1e3).toFixed(2)}K`;
    return value.toFixed(2);
  };

  const handleSort = (key: string) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const sortedData = [...devTokensData].sort((a, b) => {
    if (sortConfig.key === 'marketCap') {
      return sortConfig.direction === 'asc' ? a.marketCap - b.marketCap : b.marketCap - a.marketCap;
    }
    return 0;
  });

  if (loading) {
    return (
      <div className="rounded-lg overflow-hidden">
        <div className="p-8 text-center text-neutral-400">
          Loading dev tokens data...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg overflow-hidden">
        <div className="p-8 text-center text-red-400">
          {error}
        </div>
      </div>
    );
  }

  if (devTokensData.length === 0) {
    return (
      <div className="rounded-lg overflow-hidden">
        <div className="p-8 text-center text-neutral-400">
          No dev tokens data available
        </div>
      </div>
    );
  }

  return (
<div className="rounded-lg overflow-hidden">
  {/* Fixed Header */}
  <div className="overflow-x-auto">
    <table className="w-full">
      <thead className="bg-neutral-900/70 backdrop-blur-md">
        <tr className="border-b border-neutral-700">
          <th className="text-left px-6 py-3 text-sm font-medium text-neutral-300 min-w-[200px]">
            Token
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[80px]">
            Age
          </th>
          <th
            className="text-left px-4 py-3 text-sm font-medium text-neutral-300 cursor-pointer hover:text-white transition-colors min-w-[120px]"
            onClick={() => handleSort('marketCap')}
          >
            <div className="flex items-center gap-1">
              <ChevronDown size={14} className={sortConfig.direction === 'desc' ? 'rotate-0' : 'rotate-180'} />
              Market cap
            </div>
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[120px]">
            Remaining
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[80px] text-center">
            Holders
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[100px]">
            Dev PNL
          </th>
          <th className="text-left px-4 py-3 text-sm font-medium text-neutral-300 min-w-[180px]">
            Bonding curve progress
          </th>
        </tr>
      </thead>
    </table>
  </div>

  {/* Scrollable Body */}
  <div className="max-h-[600px] overflow-y-auto overflow-x-auto">
    <table className="w-full">
      <tbody className="divide-y divide-neutral-800">
        {sortedData.map((token) => (
          <tr key={token.id} className="hover:bg-neutral-800/50 transition-colors">
            {/* Token */}
            <td className="px-6 py-4 min-w-[200px]">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-sm flex-shrink-0">
                  {token.logo}
                </div>
                <div className="min-w-0">
                  <div className="font-medium text-white truncate">{token.token}</div>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-xs text-neutral-400 font-mono truncate">
                      {truncateAddress(token.contractAddress)}
                    </span>
                    <button
                      onClick={() => copyToClipboard(token.contractAddress)}
                      className="text-neutral-500 hover:text-neutral-300 transition-colors flex-shrink-0"
                    >
                      <Copy size={12} />
                    </button>
                    <button className="text-neutral-500 hover:text-neutral-300 transition-colors flex-shrink-0">
                      <ExternalLink size={12} />
                    </button>
                  </div>
                </div>
              </div>
            </td>

            {/* Age */}
            <td className="px-4 py-4 text-sm text-neutral-300 min-w-[80px]">{token.age}</td>

            {/* Market Cap */}
            <td className="px-4 py-4 text-sm font-mono text-white min-w-[120px]">
              {formatCurrency(token.marketCap)}
            </td>

            {/* Remaining */}
            <td className="px-4 py-4 min-w-[120px]">
              <div className="text-sm">
                <span className="text-white font-mono">
                  {formatNumber(token.remaining.amount)}
                </span>
                <span className="text-neutral-400 ml-2">{token.remaining.percentage}%</span>
              </div>
            </td>

            {/* Holders */}
            <td className="px-4 py-4 text-sm text-white text-center min-w-[80px]">
              {token.holders}
            </td>

            {/* Dev PNL */}
            <td className="px-4 py-4 text-sm min-w-[100px]">
              {token.devPnl !== null ? (
                <span className={token.devPnl < 0 ? 'text-red-400' : 'text-green-400'}>
                  {token.devPnl < 0 ? '' : '+'}{(token.devPnl * 100).toFixed(2)}%
                </span>
              ) : (
                <span className="text-neutral-500">-</span>
              )}
            </td>

            {/* Bonding Curve Progress */}
            <td className="px-4 py-4 min-w-[180px]">
              <div className="flex items-center gap-3">
                <div className="flex-1 bg-neutral-700 rounded-full h-2 min-w-[100px]">
                  <div
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${token.bondingProgress}%` }}
                  />
                </div>
                <span className="text-sm text-white font-medium min-w-[3rem] flex-shrink-0">
                  {token.bondingProgress.toFixed(1)}%
                </span>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
</div>
  );
}