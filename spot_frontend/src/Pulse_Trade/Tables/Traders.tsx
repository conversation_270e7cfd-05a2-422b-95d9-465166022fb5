import { useState, useMemo, useEffect } from 'react';
import { ChevronDown, TrendingUp, TrendingDown, Settings } from 'lucide-react';
import { useTradeData } from '@/hooks/useTradeData';

interface TradeData {
  id: string;
  age: string;
  tipAndPrio: { tip: number; priority: 'High' | 'Medium' | 'Low' };
  side: 'Buy' | 'Sell';
  priceSOL: number;
  amount: number;
  totalUSD: number;
  totalSOL: number;
  makerTxn: string;
  trader: 'Dev' | 'You' | 'Other';
}
type FilterType = 'All' | 'Dev' | 'You';



interface TradersProps {
  view?: 'dev' | 'you';
}

const Traders: React.FC<TradersProps> = ({ view: _view }) => {
  const [activeFilter] = useState<FilterType>('All');
  const [poolAddress, setPoolAddress] = useState<string | null>(null);

  // Get trade data using the custom hook
  const { trades, isLoading, isConnected, error } = useTradeData(poolAddress);

  // Get pool address from localStorage on component mount
  useEffect(() => {
    const activePulseToken = localStorage.getItem('activePulseToken');
    if (activePulseToken) {
      try {
        const tokenData = JSON.parse(activePulseToken);
        const poolAddr = tokenData.pool_address || tokenData.address || tokenData.id;
        if (poolAddr) {
          setPoolAddress(poolAddr);
          console.log('📊 Traders: Using pool address:', poolAddr);
        }
      } catch (error) {
        console.error('📊 Traders: Failed to parse activePulseToken:', error);
      }
    }
  }, []);

  // Convert trade data to TradeData format
  const convertedTrades: TradeData[] = useMemo(() => {
    return trades.map(trade => ({
      id: trade.id,
      age: trade.age,
      tipAndPrio: trade.tipAndPrio,
      side: trade.side,
      priceSOL: trade.priceSOL,
      amount: parseFloat(trade.amount.replace(/[^\d.-]/g, '')) || 0,
      totalUSD: trade.totalUSD,
      totalSOL: trade.totalSOL,
      makerTxn: trade.makerTxn,
      trader: trade.trader === 'Unknown' ? 'Other' : (trade.trader as 'Dev' | 'You' | 'Other')
    }));
  }, [trades]);

  // ---------- helpers ----------
  const filteredTrades = useMemo(() => {
    return activeFilter === 'All' ? convertedTrades : convertedTrades.filter(t => t.trader === activeFilter);
  }, [activeFilter, convertedTrades]);

  const fmtUsd = (v: number) =>
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2 }).format(v);

  const priorityCls = (p: string) =>
    ({
      High: 'text-red-400 bg-red-500/10 border-red-500/20',
      Medium: 'text-yellow-400 bg-yellow-500/10 border-yellow-500/20',
      Low: 'text-green-400 bg-green-500/10 border-green-500/20',
    } as const)[p];

  const sideCls = (s: string) => (s === 'Buy' ? 'text-green-400' : 'text-red-400');
  const sideIcon = (s: string) => (s === 'Buy' ? <TrendingUp size={14} /> : <TrendingDown size={14} />);
  const badgeCls = (t: string) =>
    ({
      You: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      Dev: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      Other: 'bg-gray-500/20 text-gray-300 border-gray-500/30',
    } as const)[t];

  // ---------- UI ----------
  return (
<div className="rounded-lg overflow-hidden">
  {/* Connection Status */}
  <div className="flex items-center justify-between px-6 py-2 bg-neutral-900/40 border-b border-neutral-700">
    <div className="flex items-center space-x-2 text-xs">
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
      <span className="text-neutral-400">
        {isLoading ? 'Loading trades...' : isConnected ? 'Live Data' : 'Disconnected'}
      </span>
      {error && <span className="text-red-400">Error: {error}</span>}
    </div>
    <div className="text-xs text-neutral-500">
      {convertedTrades.length > 0 ? `${convertedTrades.length} trades` : 'No trades available'}
    </div>
  </div>

  {/* Header */}
  <div className="overflow-x-auto">
    <table className="w-full">
      <thead>
        <tr className="border-b border-neutral-700 text-sm font-medium text-neutral-300">
          <th className="px-6 py-3 text-left min-w-[120px]">Age</th>
          <th className="px-6 py-3 text-left min-w-[100px]">Tip & Prio</th>
          <th className="px-6 py-3 text-left min-w-[80px]">Side</th>
          <th className="px-6 py-3 text-left min-w-[100px]">
            <div className="flex items-center gap-1">Price $ <ChevronDown size={12} /></div>
          </th>
          <th className="px-6 py-3 text-left min-w-[100px]">Amount</th>
          <th className="px-6 py-3 text-left min-w-[120px]">
            <div className="flex items-center gap-1">Total USD <ChevronDown size={12} /></div>
          </th>
          <th className="px-6 py-3 text-left min-w-[100px]">Total SOL</th>
          <th className="px-6 py-3 text-left min-w-[140px]">
            <div className="flex items-center gap-1">Maker Txn <Settings size={12} /></div>
          </th>
        </tr>
      </thead>
    </table>
  </div>

  {/* Body */}
  <div className="max-h-[400px] overflow-y-auto overflow-x-auto">
    <table className="w-full">
      <tbody className="divide-y divide-neutral-800/50">
        {filteredTrades.map(t => (
          <tr key={t.id} className="hover:bg-neutral-800/30 transition-colors text-sm align-middle">
            {/* Age */}
            <td className="px-6 py-4 min-w-[120px] whitespace-nowrap align-middle">
              <div className="flex items-center gap-2 font-mono text-neutral-200">
                {t.age}
                <span className={`px-2 py-0.5 rounded-full border text-xs ${badgeCls(t.trader)}`}>
                  {t.trader}
                </span>
              </div>
            </td>

            {/* Tip & Prio */}
            <td className="px-6 py-4 min-w-[100px] align-middle whitespace-nowrap">
              <div className="space-y-1">
                <div className="font-mono text-neutral-100">{t.tipAndPrio.tip.toFixed(4)} SOL</div>
                <span className={`px-2 py-0.5 rounded-full border text-xs font-medium ${priorityCls(t.tipAndPrio.priority)}`}>
                  {t.tipAndPrio.priority}
                </span>
              </div>
            </td>

            {/* Side */}
            <td className="px-6 py-4 min-w-[80px] align-middle whitespace-nowrap">
              <div className={`flex items-center gap-1 font-semibold ${sideCls(t.side)}`}>
                {sideIcon(t.side)} {t.side}
              </div>
            </td>

            {/* Price */}
            <td className="px-6 py-4 min-w-[100px] align-middle font-mono text-neutral-100 whitespace-nowrap">
              {fmtUsd(t.priceSOL)}
            </td>

            {/* Amount */}
            <td className="px-6 py-4 min-w-[100px] align-middle font-mono text-neutral-300 whitespace-nowrap">
              {t.amount.toFixed(2)} SOL
            </td>

            {/* Total USD */}
            <td className="px-6 py-4 min-w-[120px] align-middle font-mono text-neutral-100 whitespace-nowrap">
              {fmtUsd(t.totalUSD)}
            </td>

            {/* Total SOL */}
            <td className="px-6 py-4 min-w-[100px] align-middle font-mono text-neutral-300 whitespace-nowrap">
              {t.totalSOL.toFixed(2)} SOL
            </td>

            {/* Maker Txn */}
            <td className="px-6 py-4 min-w-[140px] align-middle whitespace-nowrap">
              <div className="font-mono">
                <code className="px-2 py-0.5 rounded border border-blue-500/30 bg-blue-500/10 text-blue-400">
                  {t.makerTxn}
                </code>
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
</div>

  );
};

export default Traders;
