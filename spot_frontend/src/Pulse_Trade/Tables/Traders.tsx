import { useState, useMemo, useEffect } from 'react';
import { ChevronUp, ChevronDown } from 'lucide-react';
import { useTradeData } from '@/hooks/useTradeData';
import { formatSmallNumber } from '@/utils/numberFormatting';

interface TradersProps {
  view?: 'dev' | 'you';
}

const Traders: React.FC<TradersProps> = ({ view }) => {
  const [sortAsc, setSortAsc] = useState(false); // Default to newest first

  // Get trade data using the custom hook (auto-detects pool address from localStorage)
  const { trades, isLoading, isConnected, error } = useTradeData(null);

  // Format market cap from trade data (matching trade_example.md logic)
  const formatMarketCap = (trade: any): string => {
    let marketCap = 0;

    if (trade.marketCap) {
      marketCap = trade.marketCap;
    } else if (trade.mc) {
      const mcStr = trade.mc.toString().replace(/[$,]/g, '');
      marketCap = parseFloat(mcStr) || 0;
    }

    if (marketCap >= 1e12) {
      return (marketCap / 1e12).toFixed(2) + 'T';
    } else if (marketCap >= 1e9) {
      return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
      return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
      return (marketCap / 1e3).toFixed(2) + 'K';
    } else {
      return marketCap.toFixed(0);
    }
  };

  // Format token amount from trade data (matching trade_example.md logic)
  const formatTokenAmount = (trade: any): string => {
    let tokenAmount = 0;
    let symbol = 'TOKEN';

    if (trade.tokenAmount) {
      tokenAmount = parseFloat(trade.tokenAmount.toString().replace(/[,]/g, '')) || 0;
    } else if (trade.amount) {
      tokenAmount = parseFloat(trade.amount.toString().replace(/[,]/g, '')) || 0;
    }

    let formattedAmount = '';
    if (tokenAmount >= 1e12) {
      formattedAmount = (tokenAmount / 1e12).toFixed(2) + 'T';
    } else if (tokenAmount >= 1e9) {
      formattedAmount = (tokenAmount / 1e9).toFixed(2) + 'B';
    } else if (tokenAmount >= 1e6) {
      formattedAmount = (tokenAmount / 1e6).toFixed(2) + 'M';
    } else if (tokenAmount >= 1e3) {
      formattedAmount = (tokenAmount / 1e3).toFixed(2) + 'K';
    } else {
      const amountStr = tokenAmount.toString();
      if (amountStr.includes('.')) {
        const parts = amountStr.split('.');
        const integerPart = parts[0];
        const decimalPart = parts[1];

        let leadingZeros = 0;
        for (let i = 0; i < decimalPart.length; i++) {
          if (decimalPart[i] === '0') {
            leadingZeros++;
          } else {
            break;
          }
        }

        if (leadingZeros >= 3) {
          const remainingDigits = decimalPart.substring(leadingZeros, leadingZeros + 2);
          formattedAmount = `${integerPart}.0${leadingZeros}${remainingDigits}`;
        } else {
          formattedAmount = parseFloat(tokenAmount).toFixed(2);
        }
      } else {
        formattedAmount = parseFloat(tokenAmount).toFixed(2);
      }
    }

    return `${formattedAmount} ${symbol}`;
  };

  // Format trade USD value (matching trade_example.md logic)
  const formatTradeValue = (trade: any): string => {
    let usdValue = 0;

    if (trade.totalUSD) {
      usdValue = trade.totalUSD;
    } else if (trade.tokenAmountUsd) {
      usdValue = parseFloat(trade.tokenAmountUsd.toString().replace(/[$,]/g, '')) || 0;
    } else if (trade.usdAmount) {
      usdValue = parseFloat(trade.usdAmount.toString().replace(/[$,]/g, '')) || 0;
    } else if (trade.displayValueUsd) {
      usdValue = parseFloat(trade.displayValueUsd.toString().replace(/[$,]/g, '')) || 0;
    }

    return formatSmallNumber(usdValue);
  };

  // Format transaction hash (matching trade_example.md logic)
  const formatHash = (trade: any): string => {
    let hash = '';

    if (trade.txHash) {
      hash = trade.txHash;
    } else if (trade.makerTxn && trade.makerTxn !== 'N/A') {
      hash = trade.makerTxn;
    }

    if (!hash || hash === 'N/A') {
      return 'N/A';
    }

    if (hash.length > 8) {
      const shortHash = `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
      const explorerUrl = `https://solscan.io/tx/${hash}`;
      return `<a href="${explorerUrl}" target="_blank" style="color: #3498db; text-decoration: none;">${shortHash}</a>`;
    }

    return hash;
  };

  // Format age (matching trade_example.md logic)
  const formatAge = (timestamp: number): string => {
    const now = Date.now();
    const diffSeconds = Math.floor((now - (timestamp * 1000)) / 1000);

    if (diffSeconds < 5) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  const getCurrentData = () => {
    let data = trades;

    if (view === 'dev') {
      data = trades.filter(trade => trade.trader === 'Dev');
    } else if (view === 'you') {
      data = trades.filter(trade => trade.trader === 'You');
    }

    return [...data].sort((a, b) => {
      return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    });
  };

  // ---------- UI ----------
  return (
    <div className="rounded-lg overflow-hidden">
      {/* Connection Status */}
      <div className="flex items-center justify-between px-6 py-2 bg-neutral-900/40 border-b border-neutral-700">
        <div className="flex items-center space-x-2 text-xs">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-neutral-400">
            {isLoading ? 'Loading trades...' : isConnected ? `Live • ${trades.length} trades` : 'Disconnected'}
          </span>
          {error && <span className="text-red-400">Error: {error}</span>}
        </div>
        <div className="text-xs text-neutral-500">
          {getCurrentData().length > 0 ? `${getCurrentData().length} trades` : 'No trades available'}
        </div>
      </div>

      {/* Table Structure - Matching trade_example.md */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-neutral-700 text-neutral-400 text-xs">
              <th className="text-left px-4 py-3">
                <div
                  className="flex items-center cursor-pointer hover:text-white transition-colors"
                  onClick={() => setSortAsc(!sortAsc)}
                >
                  <span>Age</span>
                  {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
                </div>
              </th>
              <th className="text-left px-4 py-3">Type</th>
              <th className="text-left px-4 py-3">Market Cap</th>
              <th className="text-left px-4 py-3">Amount</th>
              <th className="text-left px-4 py-3">USD Value</th>
              <th className="text-left px-4 py-3">Hash</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-neutral-800">
            {isLoading && (
              <tr>
                <td colSpan={6} className="text-center py-8">
                  <div className="text-neutral-400">Loading trade data...</div>
                </td>
              </tr>
            )}

            {!isLoading && getCurrentData().length === 0 && (
              <tr>
                <td colSpan={6} className="text-center py-8">
                  <div className="text-neutral-400">
                    {error ? 'Failed to load trade data' : 'No trades available'}
                  </div>
                </td>
              </tr>
            )}

            {getCurrentData().map((trade, index) => (
              <tr key={trade.id || index} className="hover:bg-neutral-800/50 transition-colors">
                {/* Age */}
                <td className="px-4 py-2 text-neutral-300 font-mono text-sm">
                  {formatAge(trade.timestamp)}
                </td>

                {/* Type */}
                <td className="px-4 py-2">
                  <span className={`font-medium ${
                    trade.type === 'buy' ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {trade.type}
                  </span>
                </td>

                {/* Market Cap */}
                <td className="px-4 py-2 text-neutral-300 font-mono">
                  ${formatMarketCap(trade)}
                </td>

                {/* Amount */}
                <td className="px-4 py-2 text-neutral-300 font-mono">
                  {formatTokenAmount(trade)}
                </td>

                {/* USD Value */}
                <td className="px-4 py-2 text-neutral-300 font-mono">
                  ${formatTradeValue(trade)}
                </td>

                {/* Hash */}
                <td className="px-4 py-2 text-blue-400">
                  <div
                    dangerouslySetInnerHTML={{ __html: formatHash(trade) }}
                    className="font-mono text-sm"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

    </div>
  );
};

export default Traders;
