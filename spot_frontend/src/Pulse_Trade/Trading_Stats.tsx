import { useState, useEffect } from 'react';

interface StatsData {
  vol: string;
  buys: number;
  buy_amt: number;
  sells: number;
  sell_amt: number;
  net: string;
}

const emptyStats: StatsData = {
  vol: '$0',
  buys: 0,
  buy_amt: 0,
  sells: 0,
  sell_amt: 0,
  net: '$0'
};

const timeframes = ['5m', '1h', '6h', '24h'];

export const TradingStats = () => {
  const [hovered, setHovered] = useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [stats, setStats] = useState<StatsData>(emptyStats);
  const [isLoading, setIsLoading] = useState(false);

  // TODO: Replace with real API call to fetch trading stats
  useEffect(() => {
    const fetchTradingStats = async () => {
      setIsLoading(true);
      try {
        // This would be replaced with actual API call
        // const response = await fetch(`/api/trading-stats/${selectedTimeframe}`);
        // const data = await response.json();
        // setStats(data);

        // For now, show empty stats instead of mock data
        setStats(emptyStats);
      } catch (error) {
        console.error('Failed to fetch trading stats:', error);
        setStats(emptyStats);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTradingStats();
  }, [selectedTimeframe]);

  const totalAmount = stats.buy_amt + stats.sell_amt || 1; // avoid div by zero
  const buyPercent = (stats.buy_amt / totalAmount) * 100;
  const sellPercent = (stats.sell_amt / totalAmount) * 100;

  return (
    <div
      className="text-white border border-[#2a2a2e] h-full relative transition-all duration-200"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
    >
      {/* Timeframe Selector */}
      <div
        className={`absolute inset-0  transition-opacity duration-200 ${
          hovered ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none px-5 py-4'
        }`}
      >
        <div className="grid grid-cols-4  text-center text-white text-sm h-full">
          {timeframes.map((tf) => (
            <button
              key={tf}
              onClick={() => setSelectedTimeframe(tf)}
              className={`p-2  transition-colors flex flex-col items-center justify-center ${
                selectedTimeframe === tf ? 'bg-[#1a1a1a]' : 'bg-transparent'
              } hover:bg-[#2a2a2e]`}
            >
              <div className="text-gray-300 text-xs">{tf}</div>
              <div className="text-pink-400 font-medium">{tf === selectedTimeframe ? stats.net : '$0'}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Stats Display */}
      <div
        className={`absolute inset-0 px-5 py-4 transition-opacity duration-200 flex flex-col justify-between ${
          hovered ? 'opacity-0 pointer-events-none' : 'opacity-100 pointer-events-auto'
        }`}
      >
        <div className="grid grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-gray-400 text-xs">{selectedTimeframe} Vol</div>
            <div className="text-white font-medium">{stats.vol}</div>
          </div>
          <div>
            <div className="text-gray-400 text-xs">Buys</div>
            <div className="text-green-400 font-medium">
              {stats.buys} / ${stats.buy_amt}
            </div>
          </div>
          <div>
            <div className="text-gray-400 text-xs">Sells</div>
            <div className="text-pink-400 font-medium">
              {stats.sells} / ${stats.sell_amt}
            </div>
          </div>
          <div>
            <div className="text-gray-400 text-xs">Net Vol.</div>
            <div className="text-pink-400 font-medium">{stats.net}</div>
          </div>
        </div>

        <div className="flex w-full mt-2 h-1 rounded overflow-hidden bg-[#333]">
          <div
            className="bg-green-500 h-full"
            style={{ width: `${buyPercent}%` }}
          />
          <div
            className="bg-pink-500 h-full"
            style={{ width: `${sellPercent}%` }}
          />
        </div>
      </div>
    </div>
  );
};
